<template>
  <div :class="$style.InspectPlanDetail" class="com-g-row-a1">
    <ComBread :data="breadData" />

    <div :class="$style.content">
      <div style="display: flex; justify-content: space-between">
        <ComTabF :tab-list="tabList" :tab="curTab" @change="handleTabChange" />
        <div :class="$style.selectinfo_btn" class="items-center" @click="back()">返回</div>
      </div>

      <component :is="curViewComp" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import ComBread from '@/components/breadcrumb/ComBread.vue';
import ComTabF from '@/components/tab/ComRadioTabF.vue';
import PlanInfo from './comp/planInfo/index.vue';
import TaskView from './comp/TaskView.vue';
import EventList from './comp/EventList.vue';
import { useRouter, useRoute } from 'vue-router';
import { IBreadData } from '@/components/breadcrumb/type.ts';
import { computed, ref } from 'vue';

const router = useRouter();
const route = useRoute();
const breadData: IBreadData[] = [{ name: '视频智能巡检' }, { name: '巡检计划' }, { name: '计划详情' }];
const tabList = [
  { name: '1', label: '计划信息' },
  { name: '2', label: '任务执行情况' },
  { name: '3', label: '事件清单' },
];

type ITab = (typeof tabList)[number]['name'];

const curTab = ref<ITab>('1');
const viewCompMap: Record<ITab, any> = {
  '1': PlanInfo,
  '2': TaskView,
  '3': EventList,
};
const curViewComp = computed(() => viewCompMap[curTab.value]);

function handleTabChange(val: ITab) {
  curTab.value = val;
}

function back() {
  router.push('/video/inspect-plan');
}

function init() {
  if (route.query.tab) {
    curTab.value = route.query.tab as ITab;
  }
}
init();
defineOptions({ name: 'InspectPlanDetail' });
</script>

<style module lang="scss">
.InspectPlanDetail {
  .content {
    display: flex;
    flex-direction: column;
    row-gap: 20px;
  }
  .selectinfo_btn {
    margin-left: 10px;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0 24px 0 24px;
    background: linear-gradient(180deg, #0d1e3b 0%, #2251a1 100%);
    border-radius: 2px;
    border: 1px solid #3371dc;

    &:hover {
      /* 添加悬停效果 */
      background: linear-gradient(180deg, #2251a1 0%, #0d1e3b 100%); /* 反转渐变色 */
      box-shadow: 0 0 5px rgba(51, 113, 220, 0.8); /* 添加发光效果 */
    }
  }
}
</style>
