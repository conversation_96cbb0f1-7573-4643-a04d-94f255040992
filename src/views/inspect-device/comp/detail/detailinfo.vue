<template>
  <div class="detail-comp">
    <div class="title" @click="isShow = !isShow">
      <div class="text-[16px]">设备信息</div>
      <div v-if="isShow"><AkChevronDown /></div>
      <div v-else><AkChevronUp /></div>
    </div>
    <div class="content" v-if="isShow">
      <n-grid :x-gap="12" :y-gap="8" :cols="1">
        <n-gi v-for="item in basicInfo" :key="item.key" :span="item.span">
          <!-- <span class="value">{{ info?.erecordDeviceInfo?.[item.key] || '' }}</span> -->
          <template v-if="item.type === 'map'">
            <span class="label">{{ item.label }}</span>
            <!--字数超过15个字，显示省略号 -->
            <span
              class="value"
              :title="
                info?.erecordDeviceInfo?.[item.key] +
                info?.erecordDeviceInfo?.['floorName'] +
                info?.erecordDeviceInfo?.['deviceAddress']
              "
            >
              {{
                (
                  info?.erecordDeviceInfo?.[item.key] +
                  info?.erecordDeviceInfo?.['floorName'] +
                  info?.erecordDeviceInfo?.['deviceAddress']
                ).length > 12
                  ? (
                      info?.erecordDeviceInfo?.[item.key] +
                      info?.erecordDeviceInfo?.['floorName'] +
                      info?.erecordDeviceInfo?.['deviceAddress']
                    ).substring(0, 12) + '...'
                  : info?.erecordDeviceInfo?.[item.key] +
                    info?.erecordDeviceInfo?.['floorName'] +
                    info?.erecordDeviceInfo?.['deviceAddress']
              }}
            </span>
            <n-button class="shrink-0 !ml-[10px]" type="primary" text size="small" @click="handleLocationClick">
              <template #icon>
                <n-icon><IconLocation /></n-icon>
              </template>
              查看位置
            </n-button>
            <!-- <span class="relative" v-if="item.type === 'map'" @click="handleLocationClick">查看位置</span> -->
          </template>
          <template v-else-if="item.key === 'brand'">
            <span class="label">{{ item.label }}</span>
            <span class="value"
              >{{ info?.erecordDeviceInfo?.[item.key] || '' }}{{ info?.erecordDeviceInfo?.['model'] || '' }}</span
            >
          </template>
          <template v-else-if="item.key === 'onlineState'">
            <span class="label">{{ item.label }}</span>
            <span class="value" style="color: #24a143ff" v-if="info?.erecordDeviceInfo?.[item.key] == '0'">在线</span>
            <span class="value" v-if="info?.erecordDeviceInfo?.[item.key] == '1'">离线</span>
          </template>
          <template v-else>
            <span class="label">{{ item.label }}</span>
            <span class="value">{{ info?.erecordDeviceInfo?.[item.key] || '' }}</span>
          </template>
        </n-gi>
      </n-grid>
    </div>
  </div>

  <div class="detail-comp">
    <div class="title" @click="isShowVideo = !isShowVideo">
      <div class="text-[16px]">实时视频</div>
      <div v-if="isShowVideo"><AkChevronDown /></div>
      <div v-else><AkChevronUp /></div>
    </div>
    <div class="content" v-if="isShowVideo">
      <div class="w-[100%] h-[200px]">
        <div class="w-[100%] h-[200px]" v-if="info?.videoPlayUrl">
          <iframe
            :src="getVideoPlayerUrl(info?.videoPlayUrl)"
            class="w-full h-full"
            frameborder="0"
            allow="autoplay; fullscreen"
          ></iframe>
        </div>
        <Empty v-else></Empty>
      </div>
    </div>
  </div>

  <div class="detail-comp">
    <div class="title" @click="isShowalgo = !isShowalgo">
      <div class="text-[16px]">视频算法</div>
      <div v-if="isShowalgo"><AkChevronDown /></div>
      <div v-else><AkChevronUp /></div>
    </div>
    <div class="content" v-if="isShowalgo">
      <div v-if="listItme.algoList.length">
        <div
          v-for="(algo, index) in listItme.algoList"
          :key="index"
          class="mt-[10px] mb-[10px] max-w-[100px] overflow-hidden text-ellipsis whitespace-nowrap"
        >
          <span class="pl-[6px] pr-[6px]" :title="algo.algoName" :style="algo.dictStyle">{{ algo.algoName }}</span>
        </div>
      </div>
      <div v-else>
        <Empty></Empty>
      </div>
    </div>
  </div>

  <div class="detail-comp" v-if="tableData.length > 0">
    <div class="title">
      <div class="text-[16px] cursor-pointer" @click="isShowtable = !isShowtable">安消联动关联设备</div>
      <div class="flex items-center cursor-pointer text-[#00B3FFFF] text-[14px] pl-[200px]" @click="handleAXLDClick">
        <img src="../../assets/locationicon.png" class="w-[16px] h-[16px] mr-[3px]" />查看位置
      </div>
      <div @click="isShowtable = !isShowtable" v-if="isShowtable"><AkChevronDown /></div>
      <div @click="isShowtable = !isShowtable" v-else><AkChevronUp /></div>
    </div>
    <div class="content" v-if="isShowtable">
      <n-data-table
        class="h-full"
        remote
        striped
        :columns="columnsDevice"
        :data="tableData"
        :bordered="false"
        :render-cell="useEmptyCell"
      />
    </div>
  </div>
  <div class="detail-comp" v-if="tableDataPlan.length > 0">
    <div class="title" @click="isShowtable1 = !isShowtable1">
      <div class="text-[16px] cursor-pointer">智能巡检计划</div>

      <div v-if="isShowtable1"><AkChevronDown /></div>
      <div v-else><AkChevronUp /></div>
    </div>
    <div class="content" v-if="isShowtable1">
      <n-data-table
        class="h-full"
        remote
        striped
        :columns="columnsPlan"
        :data="tableDataPlan"
        :bordered="false"
        :render-cell="useEmptyCell"
      />
    </div>
  </div>

  <DeviceLocation v-model:show="showDrawer" :device-list="deviceList" />
</template>

<script setup lang="ts">
import { AkChevronUp } from '@kalimahapps/vue-icons';
import { AkChevronDown } from '@kalimahapps/vue-icons';
import { computed, onMounted, ref, watch } from 'vue';
import Empty from '@/components/empty/index.vue';
import { useEmptyCell } from '@/common/hooks/useEmptyCell.ts';
import { columnsDevice, columnsPlan } from './columns.ts';
import { queryVideoPlanByVideoDeviceIdAPI } from '../../fetchData.ts';
import { useStore } from '@/store/index.ts';
import { storeToRefs } from 'pinia';
import { deviceService } from '../../deviceService.ts';
import { getVideoPlayerUrl } from '@/components/player/util.ts';
import DeviceLocation from '@/views/inspect-com/DeviceLocation.vue';
import { FlFilledLocationRipple as IconLocation } from '@kalimahapps/vue-icons';
const props = defineProps({
  listItme: {
    type: Object,
    default: () => {},
  },
});
const info = computed(() => deviceService.baseInfoForm.value);

// 安消联动关联设备
const tableData = ref(info.value.videoRelDeviceList);

const store = useStore();
const { treeAct } = storeToRefs(store);

const isShow = ref(true);
const isShowVideo = ref(true);
const isShowalgo = ref(true);
const isShowtable = ref(true);
const isShowtable1 = ref(true);

// 查看位置信息
const floorData = computed<any>(() => ({
  unitId: info.value.erecordDeviceInfo.unitId,
  buildingId: info.value.erecordDeviceInfo.buildingId,
  floorId: info.value.erecordDeviceInfo.floorId,
  floorAreaImg: window.$SYS_CFG.fileService + info.value.erecordDeviceInfo.floorAreaImg,
}));

const basicInfo = computed(() => {
  let result: any[] = [
    { label: '单位名称：', key: 'unitName', span: 1 },
    // { label: '设备编号：', key: 'deviceId', span: 1 },
    // { label: '系统类型：', key: 'deviceTypePname', span: 1 },
    // { label: '设备类型：', key: 'deviceTypeName', span: 1 },
    { label: '设备位置：', key: 'buildingName', span: 2, type: 'map' },
    { label: '品牌型号：', key: 'brand', span: 1 },
    { label: '设备编号：', key: 'deviceId', span: 1 },
    { label: '状态：', key: 'onlineState', span: 1 },
    // { label: '安装日期：', key: 'installDate', span: 1 },
  ];
  return result;
});

const tableDataPlan = ref([]);
function handleLocation() {
  console.log('位置');
}

// 智能巡检计划
function getPlanTableData() {
  queryVideoPlanByVideoDeviceIdAPI({
    pageSize: -1,
    pageNo: 1,
    deptId: treeAct.value?.id,
    deviceId: props.listItme.deviceId,
  }).then((res: any) => {
    tableDataPlan.value = res.data.rows || [];
  });
}
getPlanTableData();

// 设备信息
const deviceList = ref<any[]>([]);

const showDrawer = ref(false);
const handleLocationClick = () => {
  const data = info.value?.erecordDeviceInfo;
  deviceList.value = [
    {
      deviceId: data.deviceId,
      deviceNum: data.deviceNum,
      deviceTypeId: data.deviceTypeId,
      deviceAddress: data.deviceAddress,
      buildingId: data.buildingId,
      floorId: data.floorId,
      mapX: data.mapX,
      mapY: data.mapY,
      mapZ: data.mapZ,
      latitude: data.latitude,
      longitude: data.longitude,
    },
  ];
  showDrawer.value = true;
};

const handleAXLDClick = () => {
  const videoDevice = info.value?.erecordDeviceInfo;
  const _list = [
    {
      deviceId: videoDevice.deviceId,
      deviceNum: videoDevice.deviceNum,
      deviceTypeId: videoDevice.deviceTypeId,
      deviceAddress: videoDevice.deviceAddress,
      buildingId: videoDevice.buildingId,
      floorId: videoDevice.floorId,
      mapX: videoDevice.mapX,
      mapY: videoDevice.mapY,
      mapZ: videoDevice.mapZ,
      latitude: videoDevice.latitude,
      longitude: videoDevice.longitude,
    },
  ];

  tableData.value.forEach((item: any) => {
    _list.push({
      deviceId: item.deviceId,
      deviceNum: item.deviceNum,
      deviceTypeId: item.deviceTypeId,
      deviceAddress: item.deviceAddress,
      buildingId: item.buildingId,
      floorId: item.floorId,
      mapX: item.mapX,
      mapY: item.mapY,
      mapZ: item.mapZ,
      latitude: item.latitude,
      longitude: item.longitude,
    });
  });

  deviceList.value = _list;
  showDrawer.value = true;
};

defineOptions({ name: 'detailComp' });
</script>

<style scoped lang="scss">
.detail-comp {
  margin-top: 20px;
  width: 100%;
  background: rgba(24, 40, 71, 0.45);
  border-radius: 2px 2px 2px 2px;
  border: 1px solid #48669b;
  padding: 20px;
  .title {
    @apply flex items-center justify-between;
  }
  .content {
    @apply mt-[12px];
    .relative {
      cursor: pointer;
      border: 1px solid #dcdfe6;
      padding: 0 12px 0 12px;
      margin-left: 10px;
      &:hover {
        background-color: #0356a3;
        border: 1px solid #0356a3;
      }
    }
  }
}
</style>
