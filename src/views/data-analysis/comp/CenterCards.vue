<template>
  <div class="chartCard">
    <div class="left flex-1 pt-[16px] pl-[20px] mr-[20px]">
      <ComHeaderC title="异常事件分布" class="pageTitle" />
      <div class="flex-1 w-full h-full">
        <pieCircleChart :chartData="chartDate" />
      </div>
    </div>
    <div class="center flex-1 pt-[16px] px-[10px] mr-[20px] h-full">
      <ComHeaderC title="异常事件处置" class="pageTitle" />
      <div class="flex-1 w-full h-full">
        <EventProcessChart :chartData="eventProcessData" />
      </div>
    </div>
    <div class="right flex-1 pt-[16px] pl-[20px]">
      <ComHeaderC title="异常任务趋势" class="pageTitle" />
      <div class="flex-1 w-full h-full">
        <LineChart :chartData="taskTrendChartData" />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch, onMounted } from 'vue';
import ComHeaderC from '@/components/header/ComHeaderC.vue';
import pieCircleChart from '../echarts/pieCircleChart.vue';
import LineChart from '../echarts/LineChart.vue';
import EventProcessChart from '../echarts/EventProcessChart.vue';
import { statisticAbnormalEventDispose, statisticAbnormalTask, statisticAbnormalEvent } from '../fetchData';
import { InspectionVideoDataAnalysisXYChartData } from '../type';

interface Props {
  type?: 'eventDistribution' | 'eventProcessing' | 'taskTrend' | 'highFrequencyEvents' | 'locationTop5' | 'partTop5';
  loading?: boolean;
  orgCode?: string;
  timeRange?: string | number;
}

const chartDate = ref<any[]>([]);
const eventDisposeData = ref<InspectionVideoDataAnalysisXYChartData[]>([]);
const taskTrendChartData = ref<InspectionVideoDataAnalysisXYChartData[]>([]);

const props = withDefaults(defineProps<Props>(), {
  type: 'eventDistribution',
  loading: false,
  orgCode: '',
  timeRange: '',
});

// 异常事件处置数据
const eventProcessData = computed(() => {
  return eventDisposeData.value.map((item) => ({
    name: item.xlabel || '',
    processed: parseInt(item.y1Value || '0'),
    pending: parseInt(item.y2Value || '0'),
  }));
});

const fetchAbnormalEvent = async () => {
  if (!props.orgCode) return;

  try {
    const query = {
      orgCode: props.orgCode,
      queryPeriodType: props.timeRange,
    };

    const res = await statisticAbnormalEvent(query);
    chartDate.value = res.data || [];
  } catch (error) {
    chartDate.value = [];
  }
};

// 获取异常事件处置数据
const fetchEventDisposeData = async () => {
  if (!props.orgCode) return;

  try {
    const query = {
      orgCode: props.orgCode,
      queryPeriodType: props.timeRange,
    };

    const res = await statisticAbnormalEventDispose(query);
    eventDisposeData.value = res.data || [];
  } catch (error) {
    console.error('获取异常事件处置数据失败:', error);
    eventDisposeData.value = [];
  }
};

// 获取异常任务趋势数据
const fetchTaskTrendData = async () => {
  if (!props.orgCode) return;

  try {
    const query = {
      orgCode: props.orgCode,
      queryPeriodType: props.timeRange,
    };

    const res = await statisticAbnormalTask(query);
    taskTrendChartData.value = res.data || [];
  } catch (error) {
    console.error('获取异常任务趋势数据失败:', error);
    taskTrendChartData.value = [];
  }
};

const fetchAllData = async () => {
  await Promise.all([fetchAbnormalEvent(), fetchEventDisposeData(), fetchTaskTrendData()]);
};

onMounted(() => {
  fetchAllData();
});

// 监听参数变化，重新获取数据
watch(
  () => [props.orgCode, props.timeRange],
  () => {
    fetchAllData();
  },
  { immediate: true }
);

defineOptions({ name: 'ChartCard' });
</script>

<style scoped lang="scss">
.chartCard {
  height: 100%;
  border-radius: 8px;
  box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.06);
  display: flex;
  justify-content: space-between;

  .left,
  .center,
  .right {
    display: flex;
    flex-direction: column;
    border: 1px solid #4a5c7e;
  }
}

.chartHeader {
  padding: 16px 20px;
  border-bottom: 1px solid #4a5c7e;
  text-align: center;
  align-items: center;
}
</style>
