<template>
  <div class="chartCard">
    <div class="left flex-1 pt-[16px] pl-[20px] mr-[20px]">
      <ComHeaderC title="巡检高频事件次数分布" class="pageTitle" />
      <div class="flex-1 w-full h-full">
        <pieChart :pieData="frequencyData" />
      </div>
    </div>
    <div class="center flex-1 pt-[16px] pl-[20px] mr-[20px]">
      <ComHeaderC title="巡检高频事件位置TOP5" class="pageTitle" />
      <div class="flex-1 w-full h-full">
        <BarChart :chartData="locationChartData" />
      </div>
    </div>
    <div class="right flex-1 pt-[16px] pl-[20px]">
      <ComHeaderC title="巡检高频事件点位TOP5" class="pageTitle" />
      <div class="flex-1 w-full h-full">
        <HbarChart :chartData="deviceChartData" />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch, onMounted } from 'vue';
import ComHeaderC from '@/components/header/ComHeaderC.vue';
import pieChart from '@/views/drawing/drawingLeft/pieChart.vue';
import BarChart from '../echarts/BarChart.vue';
import HbarChart from '../echarts/HbarChart.vue';
import { genderColorConfig } from './colorList';
import { topNEventGroupByFrequency, topNEventGroupByPosition, topNEventGroupByVideoDevice } from '../fetchData';
import { InspectionVideoDataAnalysisXYChartData } from '../type';

interface Props {
  loading?: boolean;
  orgCode?: string;
  timeRange?: string | number;
}

const frequencyChartData = ref<InspectionVideoDataAnalysisXYChartData[]>([]);
const locationTop5Data = ref<InspectionVideoDataAnalysisXYChartData[]>([]);
const deviceTop5Data = ref<InspectionVideoDataAnalysisXYChartData[]>([]);
const props = withDefaults(defineProps<Props>(), {
  loading: false,
  orgCode: '',
  timeRange: 3,
});

const frequencyData = computed(() => {
  return frequencyChartData.value.map((item, index) => ({
    name: item.xlabel || '未知',
    value: item.y1Value || '0',
    totalNum: item.totalNum || '0',
    color: genderColorConfig[index] || '',
  }));
});
const locationChartData = computed(() => {
  return locationTop5Data.value.map((item) => ({
    xLabel: item.xlabel || '0',
    totalNum: item.totalNum || '0',
    y1Value: item.y1Value || [],
  }));
});

const deviceChartData = computed(() => {
  return deviceTop5Data.value.map((item, index) => ({
    xLabel: item.xlabel || '0',
    totalNum: item.totalNum || '0',
    y1Value: item.y1Value || [],
  }));
});

const fetchChartData = async () => {
  if (!props.orgCode) return;

  try {
    const query = {
      orgCode: props.orgCode,
      queryPeriodType: props.timeRange,
    };

    const [frequencyRes, locationRes, deviceRes] = await Promise.all([
      topNEventGroupByFrequency(query),
      topNEventGroupByPosition(query),
      topNEventGroupByVideoDevice(query),
    ]);

    frequencyChartData.value = frequencyRes.data || [];
    locationTop5Data.value = locationRes.data || [];
    deviceTop5Data.value = deviceRes.data || [];
  } catch (error) {
    console.error('获取图表数据失败:', error);
    frequencyChartData.value = [];
    locationTop5Data.value = [];
    deviceTop5Data.value = [];
  }
};

onMounted(() => {
  fetchChartData();
});

watch(
  () => [props.orgCode, props.timeRange],
  () => {
    fetchChartData();
  },
  { immediate: true }
);

defineExpose({ fetchChartData });
defineOptions({ name: 'ChartCard' });
</script>

<style scoped lang="scss">
.chartCard {
  height: 100%;
  border-radius: 8px;
  box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.06);
  display: flex;
  justify-content: space-between;

  .left,
  .center,
  .right {
    display: flex;
    flex-direction: column;
    border: 1px solid #4a5c7e;
  }
}

.chartHeader {
  padding: 16px 20px;
  border-bottom: 1px solid #4a5c7e;
  text-align: center;
  align-items: center;
}
</style>
