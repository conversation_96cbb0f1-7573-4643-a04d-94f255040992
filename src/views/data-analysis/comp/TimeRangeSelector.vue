<template>
  <div class="time-range-selector com-container">
    <div class="selector-content">
      <div class="preset-buttons">
        <div
          v-for="preset in timePresets"
          :key="preset.key"
          :class="['preset-button', { active: selectedPreset === preset.key }]"
          @click="handlePresetClick(preset)"
        >
          {{ preset.label }}
        </div>
      </div>
      <!-- <n-date-picker
        v-model:value="timeRange"
        type="datetimerange"
        :default-time="['00:00:00', '23:59:59']"
        :shortcuts="shortcuts"
        clearable
        class="time-picker"
        placeholder="请选择"
        @update:value="handleTimeRangeChange"
      /> -->
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, onMounted } from 'vue';
import { dayStart, dayEnd, parseTime } from '@/utils/rangeTime';

defineOptions({ name: 'TimeRangeSelector' });

const props = defineProps({
  orgCode: {
    type: String,
    default: '',
  },
});
const emits = defineEmits(['time-change']);
const selectedPreset = ref<number>(3);
const timeRange = ref<[number, number] | null>(null);

const timePresets = [
  // { key: 1, label: '今天' },
  // { key: 2, label: '昨天' },
  { key: 3, label: '近7天' },
  { key: 4, label: '近1月' },
  { key: 6, label: '近半年' },
  // { key: 99, label: '自定义' },
];

function getTimeRangeByPreset(presetKey: number): [number, number] | null {
  const now = new Date();

  switch (presetKey) {
    case 1: {
      // 今天
      const start = dayStart(now) as string;
      const end = dayEnd(now) as string;
      return [new Date(start).getTime(), new Date(end).getTime()];
    }
    case 2: {
      // 昨天
      const yesterday = new Date(now);
      yesterday.setDate(yesterday.getDate() - 1);
      const start = dayStart(yesterday) as string;
      const end = dayEnd(yesterday) as string;
      return [new Date(start).getTime(), new Date(end).getTime()];
    }
    case 3: {
      // 近一周
      const weekAgo = new Date(now);
      weekAgo.setDate(weekAgo.getDate() - 7);
      const start = dayStart(weekAgo) as string;
      const end = dayEnd(now) as string;
      return [new Date(start).getTime(), new Date(end).getTime()];
    }
    case 4: {
      // 近一个月
      const monthAgo = new Date(now);
      monthAgo.setMonth(monthAgo.getMonth() - 1);
      const start = dayStart(monthAgo) as string;
      const end = dayEnd(now) as string;
      return [new Date(start).getTime(), new Date(end).getTime()];
    }
    case 5: {
      // 近三个月
      const threeMonthsAgo = new Date(now);
      threeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - 3);
      const start = dayStart(threeMonthsAgo) as string;
      const end = dayEnd(now) as string;
      return [new Date(start).getTime(), new Date(end).getTime()];
    }
    default:
      return null;
  }
}

function handlePresetClick(preset: { key: number; label: string }) {
  selectedPreset.value = preset.key;

  if (preset.key === 99) {
    emitTimeChange(timeRange.value);
    return;
  }

  // const range = getTimeRangeByPreset(preset.key);
  // timeRange.value = range;
  emits('time-change', {
    queryPeriodType: selectedPreset.value,
  });
}

function handleTimeRangeChange(value: [number, number] | null) {
  if (value && selectedPreset.value !== 99) {
    selectedPreset.value = 99;
  }
  if (!value) {
    selectedPreset.value = 1;
  }
  emitTimeChange(value);
}

function emitTimeChange(value: [number, number] | null) {
  const params = {
    queryPeriodType: selectedPreset.value,
  } as any;

  // 如果是自定义时间，则传递具体的开始和结束时间
  if (selectedPreset.value === 99 && value) {
    params.queryPeriodStartDateTime = parseTime(value[0], '{y}-{m}-{d} {h}:{i}:{s}');
    params.queryPeriodEndDateTime = parseTime(value[1], '{y}-{m}-{d} {h}:{i}:{s}');
  }

  emits('time-change', params);
}

// 监听时间范围变化
watch(
  () => timeRange.value,
  (newValue) => {
    emitTimeChange(newValue);
  },
  { immediate: false }
);

watch(
  () => props.orgCode,
  () => {
    selectedPreset.value = 3;
  },
  { immediate: false }
);

// 组件挂载时设置默认时间范围（今日）
onMounted(() => {
  handlePresetClick({ key: 3, label: '近7天' });
});
</script>

<style scoped lang="scss">
.time-range-selector {
  padding: 15px 0;
  display: flex;
  flex-direction: column;

  .selector-content {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 12px;
    height: 100%;
  }

  .preset-buttons {
    display: flex;
    overflow: hidden;
    border: 1px solid #3c516f;
    height: 34px;

    .preset-button {
      padding: 0 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      cursor: pointer;
      background: rgba(30, 39, 56, 0.65);
      color: #fff;
      border-right: 1px solid rgba(60, 81, 111, 1);
      transition: all 0.3s;
      white-space: nowrap;

      &:last-child {
        border-right: none;
      }

      &.active {
        background: rgba(28, 111, 255, 0.4);
        border-radius: 2px 0px 0px 2px;
        color: #fff;
      }
    }
  }

  .time-picker {
    min-width: 280px;
  }
}
</style>
