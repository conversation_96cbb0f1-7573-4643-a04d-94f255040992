<template>
  <div class="event-process-chart h-full">
    <div class="scroll-container">
      <div v-for="item in chartData" :key="item.name" class="event-item">
        <div class="processed-section">
          <div class="progress-bar" :style="{ width: `${getProgressWidth(item.processed)}%` }"></div>
          <div class="label">已处置 {{ item.processed }}</div>
        </div>

        <div class="event-name">{{ item.name }}</div>

        <div class="pending-section">
          <div class="progress-bar" :style="{ width: `${getProgressWidth(item.pending)}%` }"></div>
          <div class="label">待处置 {{ item.pending }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue';

defineOptions({ name: 'EventProcessChart' });

interface EventProcessData {
  name: string;
  processed: number;
  pending: number;
}

const props = defineProps<{
  chartData: EventProcessData[];
}>();

const maxValue = computed(() => {
  if (!props.chartData || props.chartData.length === 0) return 1;
  const allValues = props.chartData.flatMap((item) => [item.processed, item.pending]);
  return Math.max(...allValues, 1);
});

function getProgressWidth(value: number): number {
  if (value <= 0) return 0;
  const percentage = (value / maxValue.value) * 100;
  return Math.max(percentage, value > 0 ? 5 : 0);
}
</script>

<style scoped lang="scss">
.event-process-chart {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;

  .scroll-container {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    display: flex;
    flex-direction: column;
    gap: 16px;
    padding-right: 4px;

    // 自定义滚动条样式
    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-track {
      background: rgba(255, 255, 255, 0.1);
      border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(255, 255, 255, 0.3);
      border-radius: 2px;

      &:hover {
        background: rgba(255, 255, 255, 0.5);
      }
    }
  }

  .event-item {
    display: flex;
    align-items: center;
    gap: 12px;
    min-height: 40px;
    flex-shrink: 0;

    .processed-section {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      gap: 4px;

      .progress-bar {
        height: 8px;
        background: #097256;
        border-radius: 88px 88px 88px 88px;
        border: 1px solid #00b282;
        min-width: 2px;
      }
    }

    .event-name {
      font-size: 14px;
      font-weight: 500;
      color: var(--skin-t1, #ffffff);
      text-align: center;
      min-width: 80px;
    }

    .pending-section {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      gap: 4px;

      .progress-bar {
        height: 8px;
        background: #8c4c1e;
        border-radius: 88px 88px 88px 88px;
        border: 1px solid #f6814f;
        min-width: 2px;
      }
    }
  }

  .label {
    font-size: 12px;
    color: rgba(212, 232, 255, 0.8);
  }
}
</style>
