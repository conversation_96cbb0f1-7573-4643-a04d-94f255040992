<template>
  <div class="left-comp h-full w-full flex items-start" v-if="hasData">
    <div class="flex items-center justify-center flex-1 h-full">
      <div class="bg flex flex-col items-center justify-center">
        <progressCom :data="indicators" />
        <div class="mb-5px">
          <span class="value mr-[2px]">{{ totalCount }}</span>
          <span class="unit">起</span>
        </div>
      </div>
    </div>
    <div class="right h-full flex-1 flex flex-col items-center">
      <div class="indicators-list flex-1">
        <div v-for="item in indicators" :key="item.name" class="indicator-item">
          <div class="flex items-center">
            <div class="indicator-dot mr-[10px]" :style="{ backgroundColor: item.color }"></div>
            <div class="indicator-label" :title="item.name">
              {{ item.name.length > 5 ? item.name.substring(0, 5) + '...' : item.name }}
            </div>
          </div>
          <div class="flex">
            <div class="indicator-value mr-[10px]">{{ item.value }}{{ item.unit }}</div>
            <div class="indicator-percent">{{ getPercentage(item.value) }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <Empty v-else />
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import progressCom from './progress.vue';
import Empty from '@/components/empty/index.vue';

interface Props {
  chartData?: any;
}

const props = withDefaults(defineProps<Props>(), {
  chartData: null,
});

const indicators = ref<{ name: string; progress: number; value: number; unit: string; color: any; status: number }[]>(
  []
);

const hasData = computed(() => {
  return Array.isArray(props.chartData) && props.chartData.length > 0 && indicators.value.length > 0;
});
const genderColorConfig = [
  'rgba(241, 67, 67, 1)',
  'rgba(246, 129, 79, 1)',
  'rgba(235, 166, 69, 1)',
  'rgba(97, 127, 247, 1)',
  'rgba(76, 173, 255, 1)',
  'rgba(130, 190, 232, 1)',
];
const totalCount = computed(() => {
  return indicators.value.reduce((sum, item) => sum + item.value, 0);
});

// 计算每个指标的百分比
const getPercentage = (value: number) => {
  if (totalCount.value === 0) return '0%';
  return Math.round((value / totalCount.value) * 100) + '%';
};

// 监听props变化，更新数据
watch(
  () => props.chartData,
  (newData) => {
    indicators.value = [];

    if (Array.isArray(newData) && newData.length > 0) {
      newData.forEach((item, index) => {
        const value = Number(item.y1Value) || 0;
        const progress = Number(item.y2Value) || 0;

        indicators.value.push({
          name: item.xlabel || '未知',
          progress: progress,
          value: value,
          unit: '',
          color: genderColorConfig[index] || '#9B6FDC',
          status: index,
        });
      });
    }
  },
  { immediate: true }
);
</script>

<style scoped lang="scss">
.bg {
  width: 115px;
  height: 115px;
  background: url('../assets/circle-bg.png') no-repeat center center;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;
  position: relative;

  .value {
    font-family: D-DIN-PRO, D-DIN-PRO;
    font-weight: 500;
    font-size: 20px;
    color: #ffffff;
  }

  .unit {
    font-weight: 400;
  }
}

.indicators-list {
  width: 100%;
  padding: 0 10px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  .indicator-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    .indicator-dot {
      width: 13px;
      height: 13px;
      margin-top: 2px;
    }
  }
}
</style>
