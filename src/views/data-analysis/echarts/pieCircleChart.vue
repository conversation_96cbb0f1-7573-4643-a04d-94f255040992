<template>
  <div class="left-comp h-full w-full flex items-start">
    <div class="flex items-center justify-center flex-1 h-full">
      <div class="bg flex flex-col items-center justify-center">
        <progressCom :data="indicators" />
        <div class="mb-5px">
          <span class="value">{{ mainIndicator.avgScore || 0 }}</span>
          <span>起</span>
        </div>
      </div>
    </div>
    <div class="right h-full flex-1 flex flex-col items-center">
      <div class="indicators-list flex-1 mt-[20px]">
        <div v-for="item in indicators" :key="item.name" class="indicator-item">
          <div class="flex items-center">
            <div class="indicator-dot mr-[10px]" :style="{ backgroundColor: item.color }"></div>
            <div class="indicator-label">{{ item.name }}</div>
          </div>
          <div class="flex">
            <div class="indicator-value mr-[10px]">{{ item.value }}{{ item.unit }}</div>
            <div class="indicator-percent">{{ getPercentage(item.value) }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import progressCom from './progress.vue';
import { genderColorConfig } from '../comp/colorList';

interface Props {
  chartData?: any;
}

const props = withDefaults(defineProps<Props>(), {
  chartData: null,
});

const mainIndicator = ref<{ avgScore: string; assessTime: string; scoreLevel: string }>({
  avgScore: '',
  assessTime: '',
  scoreLevel: '',
});

const indicators = ref<{ name: string; progress: number; value: number; unit: string; color: any; status: number }[]>(
  []
);

// 计算总数
const totalCount = computed(() => {
  return indicators.value.reduce((sum, item) => sum + item.value, 0);
});

// 计算每个指标的百分比
const getPercentage = (value: number) => {
  if (totalCount.value === 0) return '0%';
  return Math.round((value / totalCount.value) * 100) + '%';
};

// 监听props变化，更新数据
watch(
  () => props.chartData,
  (newData) => {
    if (Array.isArray(newData) && newData.length > 0) {
      indicators.value = [];
      newData.forEach((item, index) => {
        const value = Number(item.y1Value) || 0;
        const progress = Number(item.y2Value) || 0;

        indicators.value.push({
          name: item.xlabel || '未知',
          progress: progress,
          value: value,
          unit: '',
          color: genderColorConfig[index] || '#9B6FDC',
          status: index,
        });
      });
    }
  },
  { immediate: true }
);
</script>

<style scoped lang="scss">
.bg {
  width: 115px;
  height: 115px;
  background: url('../assets/circle-bg.png') no-repeat center center;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;
  position: relative;

  .values {
    width: 118px;
    height: 118px;
    position: relative;

    .percent {
      font-size: 18px;
      font-weight: 600;
    }

    .value {
      font-family: D-DIN-PRO, D-DIN-PRO;
      font-weight: 500;
      font-size: 20px;
      color: #ffffff;
    }
  }
}

.indicators-list {
  width: 100%;
  padding: 0 15px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  .indicator-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 17px;
    .indicator-dot {
      width: 13px;
      height: 13px;
      margin-top: 2px;
    }
  }
}
</style>
