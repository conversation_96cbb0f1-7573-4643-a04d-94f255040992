<template>
  <div class="chart-container w-full h-full" ref="barChart"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, markRaw, onBeforeUnmount } from 'vue';
import { useEchartsResizeObserver } from '@/common/hooks/useEchartsResizeObserver';

import * as echarts from 'echarts';

const props = defineProps({
  // 图表标题
  title: {
    type: String,
    default: '',
  },
  xAxis: {
    type: String,
    default: 'category',
  },
  yAxis: {
    type: String,
    default: 'value',
  },
  // 图表数据
  chartData: {
    type: Array,
    default: () => [] as any,
    // 数据格式: [{name: '名称', value: 数值, color: {渐变色配置}}]
  },
  // 图例数据
  legendData: {
    type: Array,
    default: () => [],
  },
  // 是否显示图例
  showLegend: {
    type: Boolean,
    default: true,
  },
});

const barChart = ref();
const myChart = ref<any>(null);
const observer = ref<ResizeObserver>();

const handleResize = () => {
  myChart.value && myChart.value.resize();
};

const renderChart = () => {
  if (myChart.value) destroyEcharts();
  // 标记一个对象，使其永远不会再成为响应式对象
  myChart.value = markRaw(echarts.init(barChart.value));
  const option = {
    tooltip: {
      trigger: 'item',
    },
    grid: {
      left: '1%',
      right: '4%',
      top: '10%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: props.xAxis,
      axisTick: {
        show: false,
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: 'rgba(66, 80, 108, 0.15)',
        },
      },
      axisLabel: {
        show: true,
        fontWeight: 400,
        fonSize: 12,
        color: 'rgba(212, 232, 255, 0.8)',
        rotate: 40,
        formatter: function (value: string) {
          return value.length > 5 ? value.substring(0, 4) + '..' : value;
        },
      },
      data: ['Mon3232', 'Tue322', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
    },
    yAxis: {
      type: props.yAxis,
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: 'rgba(66, 80, 108, 0.15)',
          type: 'dashed',
        },
      },
      axisLabel: {
        show: true,
        fontWeight: 400,
        fonSize: 12,
        color: 'rgba(212, 232, 255, 0.8)',
      },
    },
    series: [
      // 背景柱子系列 - 显示背景图
      {
        type: 'bar',
        data: props.chartData[0].y1Value, // 背景柱子高度设为最大值
        barWidth: '50%', // 柱子宽度
        barGap: '-100%', // 负值让柱子重叠
        itemStyle: {
          color: ' rgba(255,255,255,0.25)',
          opacity: 0.3, // 背景图透明度
        },
        z: 1, // 层级较低，作为背景
      },
      // 数据柱子系列 - 显示实际数据
      {
        type: 'bar',
        data: [65, 45, 78, 52, 88], // 实际数据
        barWidth: '30%', // 与背景柱子相同宽度
        barGap: '-80%', // 负值让柱子重叠
        itemStyle: {
          color: new echarts.graphic.LinearGradient(
            0,
            0,
            0,
            1, // 渐变方向：0,0(上) 到 0,1(下)
            [
              { offset: 0, color: '#0080D0' }, // 顶部颜色
              { offset: 1, color: '#004770' }, // 底部颜色
            ]
          ),
        },
        z: 2, // 层级较高，覆盖在背景上
      },
    ],
  };

  myChart.value.setOption(option);

  observer.value = useEchartsResizeObserver(myChart, barChart).observer;
};

function destroyEcharts() {
  if (myChart.value) {
    myChart.value.dispose();
    myChart.value = null;
  }
}

// 监听数据变化，重新渲染图表
watch(
  () => props.chartData,
  () => {
    renderChart();
  },
  { deep: true }
);

onMounted(() => {
  if (barChart.value) {
    myChart.value = echarts.init(barChart.value);
    renderChart();

    window.addEventListener('resize', handleResize);
  }
});

onBeforeUnmount(() => {
  destroyEcharts();
  // 在组件卸载时停止监听，避免内存泄漏
  observer.value?.disconnect();
});

onUnmounted(() => {
  if (myChart.value) {
    myChart.value.dispose();
    window.removeEventListener('resize', handleResize);
  }
});

defineOptions({ name: 'BarChartComponent' });
</script>

<style scoped>
.chart-container {
  width: 100%;
  height: 100%;
}
</style>
