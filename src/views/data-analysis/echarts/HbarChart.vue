<template>
  <div class="chart-container w-full h-full" ref="barChart" v-if="props.chartData.length"></div>
  <Empty v-else></Empty>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, markRaw, onBeforeUnmount, nextTick } from 'vue';
import { useEchartsResizeObserver } from '@/common/hooks/useEchartsResizeObserver';
import Empty from '@/components/empty/index.vue';

import * as echarts from 'echarts';

const props = defineProps({
  // 图表标题
  title: {
    type: String,
    default: '',
  },
  // 图表数据
  chartData: {
    type: Array as () => any[],
    required: true,
    // 数据格式: [{name: '名称', value: 数值, color: {渐变色配置}}]
  },
  // 图例数据
  legendData: {
    type: Array,
    default: () => [],
  },
  // 是否显示图例
  showLegend: {
    type: Boolean,
    default: true,
  },
});

const barChart = ref();
const myChart = ref<any>(null);
const observer = ref<ResizeObserver>();

const handleResize = () => {
  if (myChart.value) {
    nextTick(() => {
      myChart.value?.resize();
    });
  }
};

const renderChart = () => {
  if (!props.chartData.length || !barChart.value) {
    return;
  }
  if (myChart.value) destroyEcharts();
  // 标记一个对象，使其永远不会再成为响应式对象
  myChart.value = markRaw(echarts.init(barChart.value));
  const option = {
    tooltip: {
      trigger: 'item',
    },
    grid: {
      left: '1%',
      right: '4%',
      top: '10%',
      bottom: '3%',
      containLabel: true,
    },
    yAxis: {
      type: 'category',
      axisLine: {
        show: true,
        lineStyle: {
          color: 'rgba(66, 80, 108, 0.15)',
        },
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: 'rgba(66, 80, 108, 0.15)',
          type: 'dashed',
        },
      },
      axisLabel: {
        show: true,
        fontWeight: 400,
        fonSize: 12,
        color: 'rgba(212, 232, 255, 0.8)',
        formatter: function (value: string) {
          return value.length > 5 ? value.substring(0, 4) + '..' : value;
        },
      },
      data: props.chartData.map((item: any) => item.xLabel) || [],
    },
    xAxis: {
      type: 'value',
      axisLine: {
        show: true,
        lineStyle: {
          color: 'rgba(66, 80, 108, 0.15)',
        },
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: 'rgba(66, 80, 108, 0.15)',
          type: 'dashed',
        },
      },
      axisLabel: {
        show: true,
        fontWeight: 400,
        fonSize: 12,
        color: 'rgba(212, 232, 255, 0.8)',
        formatter: function (value: string) {
          return value.length > 5 ? value.substring(0, 4) + '..' : value;
        },
      },
    },
    series: [
      {
        type: 'bar',
        data: props.chartData.map((item: any) => item.y1Value) || [],
        barWidth: '40%',
        itemStyle: {
          color: new echarts.graphic.LinearGradient(1, 0, 0, 0, [
            { offset: 0, color: '#0080D0' }, // 顶部颜色
            { offset: 1, color: '#004770' }, // 底部颜色
          ]),
        },
      },
    ],
  };

  myChart.value.setOption(option);

  observer.value = useEchartsResizeObserver(myChart, barChart).observer;
};

function destroyEcharts() {
  if (myChart.value) {
    myChart.value.dispose();
    myChart.value = null;
  }
}

// 监听数据变化，重新渲染图表
watch(
  () => props.chartData,
  async () => {
    await nextTick();
    renderChart();
  },
  { deep: true }
);

onMounted(async () => {
  // 等待DOM更新完成
  await nextTick();
  if (barChart.value) {
    myChart.value = echarts.init(barChart.value);
    renderChart();

    window.addEventListener('resize', handleResize);
    setTimeout(() => {
      handleResize();
    }, 100);
  }
});

onBeforeUnmount(() => {
  destroyEcharts();
  // 在组件卸载时停止监听，避免内存泄漏
  observer.value?.disconnect();
});

onUnmounted(() => {
  if (myChart.value) {
    myChart.value.dispose();
    window.removeEventListener('resize', handleResize);
  }
});

defineOptions({ name: 'BarChartComponent' });
</script>

<style scoped>
.chart-container {
  width: 100%;
  height: 100%;
}
</style>
