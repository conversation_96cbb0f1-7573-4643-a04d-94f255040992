<template>
  <div class="chart-container w-full h-full" ref="barChart" v-if="props.chartData.length"></div>
  <Empty v-else></Empty>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, markRaw, onBeforeUnmount, nextTick } from 'vue';
import { useEchartsResizeObserver } from '@/common/hooks/useEchartsResizeObserver';
import Empty from '@/components/empty/index.vue';

import * as echarts from 'echarts';

const props = defineProps({
  // 图表标题
  title: {
    type: String,
    default: '',
  },
  xAxis: {
    type: String,
    default: 'category',
  },
  yAxis: {
    type: String,
    default: 'value',
  },
  // 图表数据
  chartData: {
    type: Array,
    required: true,
    // 数据格式: [{name: '名称', value: 数值, color: {渐变色配置}}]
  },
  // 图例数据
  legendData: {
    type: Array,
    default: () => [],
  },
  // 是否显示图例
  showLegend: {
    type: Boolean,
    default: true,
  },
});

const barChart = ref();
const myChart = ref<any>(null);
const observer = ref<ResizeObserver>();

const handleResize = () => {
  myChart.value && myChart.value.resize();
};

const renderChart = () => {
  if (!props.chartData.length || !barChart.value) {
    return;
  }
  if (myChart.value) destroyEcharts();
  // 标记一个对象，使其永远不会再成为响应式对象
  myChart.value = markRaw(echarts.init(barChart.value));
  const option = {
    tooltip: {
      trigger: 'axis',
    },
    legend: {
      right: '4%',
      textStyle: {
        color: '#fff',
      },
      data: ['异常任务', '正常任务'],
    },
    grid: {
      left: '0',
      right: '4%',
      top: '13%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      axisTick: {
        show: false,
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: 'rgba(66, 80, 108, 0.15)',
        },
      },
      axisLabel: {
        show: true,
        fontWeight: 400,
        fonSize: 12,
        color: 'rgba(212, 232, 255, 0.8)',
        // formatter: function (value: string) {
        //   return value.length > 5 ? value.substring(0, 4) + '..' : value;
        // },
      },
      data: props.chartData.map((item: any) => item.xlabel) || [],
    },
    yAxis: {
      type: 'value',
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: 'rgba(66, 80, 108, 0.15)',
          type: 'dashed',
        },
      },
      axisLabel: {
        show: true,
        fontWeight: 400,
        fonSize: 12,
        color: 'rgba(212, 232, 255, 0.8)',
      },
    },
    series: [
      {
        name: '异常任务',
        type: 'line',
        symbol: 'none',
        areaStyle: {
          color: new echarts.graphic.LinearGradient(
            0,
            1,
            0,
            0, // 从下到上的渐变
            [
              { offset: 0, color: 'rgba(135, 206, 235, 0.01)' }, // 底部浅色（透明度低）
              { offset: 1, color: 'rgba(70, 130, 180, 0.7)' }, // 顶部深色（透明度高）
            ]
          ), // 应用渐变色
        },
        data: props.chartData.map((item: any) => item.y1Value) || [],
      },
      {
        name: '正常任务',
        type: 'line',
        symbol: 'none',
        areaStyle: {
          color: new echarts.graphic.LinearGradient(
            0,
            1,
            0,
            0, // 从下到上的渐变
            [
              { offset: 0, color: 'rgba(135, 206, 235, 0.01)' }, // 底部浅色（透明度低）
              { offset: 1, color: 'rgba(70, 130, 180, 0.7)' }, // 顶部深色（透明度高）
            ]
          ), // 应用渐变色
        },
        data: props.chartData.map((item: any) => item.y2Value) || [],
      },
    ],
  };

  myChart.value.setOption(option);

  observer.value = useEchartsResizeObserver(myChart, barChart).observer;
};

function destroyEcharts() {
  if (myChart.value) {
    myChart.value.dispose();
    myChart.value = null;
  }
}

// 监听数据变化，重新渲染图表
watch(
  () => props.chartData,
  async () => {
    await nextTick();
    renderChart();
  },
  { deep: true }
);

onMounted(async () => {
  await nextTick();
  // 只有在有数据且DOM元素存在时才初始化图表
  if (props.chartData.length && barChart.value) {
    renderChart();
    window.addEventListener('resize', handleResize);
  }
});

onBeforeUnmount(() => {
  destroyEcharts();
  // 在组件卸载时停止监听，避免内存泄漏
  observer.value?.disconnect();
});

onUnmounted(() => {
  if (myChart.value) {
    myChart.value.dispose();
    window.removeEventListener('resize', handleResize);
  }
});

defineOptions({ name: 'LineChartComponent' });
</script>

<style scoped>
.chart-container {
  width: 100%;
  height: 100%;
}
</style>
