<template>
  <div class="com-g-row-a1">
    <com-bread :data="breadData" />
    <div class="com-g-col-a1">
      <ComOrgTreeWrap @change="handleChange" @toggle="handleTreeToggle" />
      <div class="com-g-row-aa1">
        <TopCards class="h-full" :orgCode="selectedOrgCode" ref="topCardsRef" />
        <TimeRangeSelector class="h-full" @time-change="handleTimeChange" :orgCode="selectedOrgCode" />
        <CenterCards class="h-full" :orgCode="selectedOrgCode" :timeRange="selectedTimeRange" ref="centerCardsRef" />
        <div></div>
        <ChartCard class="h-full" :orgCode="selectedOrgCode" :timeRange="selectedTimeRange" ref="chartCardRef" />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import ComBread from '@/components/breadcrumb/ComBread.vue';
import ComOrgTreeWrap from '@/components/tree/OrgTreeWrap.vue';
import CenterCards from './comp/CenterCards.vue';
import ChartCard from './comp/ChartCard.vue';
import TopCards from './comp/TopCards.vue';
import TimeRangeSelector from './comp/TimeRangeSelector.vue';
import { IBreadData } from '@/components/breadcrumb/type.ts';

const breadData = ref<IBreadData[]>([{ name: '视频智能巡检' }, { name: '数据分析' }]);
const selectedOrgCode = ref<string>('');
const selectedTimeRange = ref<number>(3);
const topCardsRef = ref<InstanceType<typeof TopCards>>();
const chartCardRef = ref<InstanceType<typeof ChartCard>>();
const centerCardsRef = ref<InstanceType<typeof CenterCards>>();

function handleChange(val: any) {
  selectedOrgCode.value = val?.id || '';
  selectedTimeRange.value = 3;
}

function handleTimeChange(timeRange: any) {
  selectedTimeRange.value = timeRange.queryPeriodType;
}

// 处理树结构显示隐藏切换，触发图表重新渲染
function handleTreeToggle(toggleData: { isNarrow: boolean }) {
  console.log('树结构切换状态:', toggleData);

  // 触发所有图表组件的重新渲染
  setTimeout(() => {
    // 触发TopCards组件的重新渲染
    if (topCardsRef.value && typeof topCardsRef.value.fetchOverViewData === 'function') {
      topCardsRef.value.fetchOverViewData();
    }

    // 触发ChartCard组件的重新渲染
    if (chartCardRef.value && typeof chartCardRef.value.fetchChartData === 'function') {
      chartCardRef.value.fetchChartData();
    }

    // 触发窗口resize事件，让所有图表自动调整大小
    window.dispatchEvent(new Event('resize'));
  }, 50); // 短暂延迟确保DOM更新完成
}

defineOptions({ name: 'DataAnalysisIndex' });
</script>

<style scoped lang="scss">
.com-g-row-aa1 {
  grid-template-rows: 19% 60px 1fr 20px 1fr;
}
</style>
