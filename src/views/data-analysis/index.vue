<template>
  <div class="com-g-row-a1">
    <com-bread :data="breadData" />
    <div class="com-g-col-a1">
      <ComOrgTreeWrap @change="handleChange" />
      <div class="com-g-row-aa1">
        <TopCards class="h-full" :orgCode="selectedOrgCode" ref="topCardsRef" />
        <TimeRangeSelector class="h-full" @time-change="handleTimeChange" :orgCode="selectedOrgCode" />
        <CenterCards class="h-full" />
        <div></div>
        <ChartCard class="h-full" :orgCode="selectedOrgCode" :timeRange="selectedTimeRange" ref="chartCardRef" />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import ComBread from '@/components/breadcrumb/ComBread.vue';
import ComOrgTreeWrap from '@/components/tree/OrgTreeWrap.vue';
import CenterCards from './comp/CenterCards.vue';
import ChartCard from './comp/ChartCard.vue';
import TopCards from './comp/TopCards.vue';
import TimeRangeSelector from './comp/TimeRangeSelector.vue';
import { IBreadData } from '@/components/breadcrumb/type.ts';

const breadData = ref<IBreadData[]>([{ name: '视频智能巡检' }, { name: '数据分析' }]);
const selectedOrgCode = ref<string>('');
const selectedTimeRange = ref<number>(3);
const topCardsRef = ref<InstanceType<typeof TopCards>>();
const chartCardRef = ref<InstanceType<typeof ChartCard>>();

function handleChange(val: any) {
  selectedOrgCode.value = val?.id || '';
  selectedTimeRange.value = 3;
}

function handleTimeChange(timeRange: any) {
  selectedTimeRange.value = timeRange.queryPeriodType;
}

defineOptions({ name: 'DataAnalysisIndex' });
</script>

<style scoped lang="scss">
.com-g-row-aa1 {
  grid-template-rows: 19% 60px 1fr 20px 1fr;
}
</style>
