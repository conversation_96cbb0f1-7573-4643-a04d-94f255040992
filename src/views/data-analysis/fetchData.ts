import { $http } from '@tanzerfe/http';
import { api } from '@/api';
import { IObj } from '@/types';
import { InspectionVideoOverview, InspectionVideoDataAnalysisXYChartData } from './type';

//智能巡检计划情况
export function overView(query: IObj<any>) {
  const params = api.getComParams(api.type.intelligent, api.name.dataAnalysis.intelligentOver, query);

  return $http.post<InspectionVideoOverview>(params.url, { data: { _cfg: { showTip: true }, ...params.data } });
}

//巡检高频事件次数分布
export function topNEventGroupByFrequency(query: IObj<any>) {
  const params = api.getComParams(api.type.intelligent, api.name.dataAnalysis.topNEventGroupByFrequency, query);

  return $http.post<InspectionVideoDataAnalysisXYChartData[]>(params.url, {
    data: { _cfg: { showTip: true }, ...params.data },
  });
}

//巡检高频事件位置TOP5
export function topNEventGroupByPosition(query: IObj<any>) {
  const params = api.getComParams(api.type.intelligent, api.name.dataAnalysis.topNEventGroupByPosition, query);

  return $http.post<InspectionVideoDataAnalysisXYChartData[]>(params.url, {
    data: { _cfg: { showTip: true }, ...params.data },
  });
}

//巡检高频事件点位TOP5
export function topNEventGroupByVideoDevice(query: IObj<any>) {
  const params = api.getComParams(api.type.intelligent, api.name.dataAnalysis.topNEventGroupByVideoDevice, query);

  return $http.post<InspectionVideoDataAnalysisXYChartData[]>(params.url, {
    data: { _cfg: { showTip: true }, ...params.data },
  });
}

// 异常事件分布
export function statisticAbnormalEvent(query: IObj<any>) {
  const params = api.getComParams(api.type.intelligent, api.name.dataAnalysis.statisticAbnormalEvent, query);

  return $http.post<InspectionVideoDataAnalysisXYChartData[]>(params.url, {
    data: { _cfg: { showTip: true }, ...params.data },
  });
}

//异常事件处置统计
export function statisticAbnormalEventDispose(query: IObj<any>) {
  const params = api.getComParams(api.type.intelligent, api.name.dataAnalysis.statisticAbnormalEventDispose, query);

  return $http.post<InspectionVideoDataAnalysisXYChartData[]>(params.url, {
    data: { _cfg: { showTip: true }, ...params.data },
  });
}

//异常任务趋势统计
export function statisticAbnormalTask(query: IObj<any>) {
  const params = api.getComParams(api.type.intelligent, api.name.dataAnalysis.statisticAbnormalTask, query);

  return $http.post<InspectionVideoDataAnalysisXYChartData[]>(params.url, {
    data: { _cfg: { showTip: true }, ...params.data },
  });
}
