<template>
  <div :class="$style.waterEvalContainer" v-if="props.pieData.length">
    <div :class="$style.cityGreenLandCharts" id="cityGreenLand-charts"></div>
  </div>
  <Empty v-else></Empty>
</template>
<script lang="ts" setup>
import { onMounted, onUnmounted, nextTick, ref, watch } from 'vue';
import * as echarts from 'echarts';
import 'echarts-gl';
import { useEchartsResizeObserver } from '@/common/hooks/useEchartsResizeObserver';
import Empty from '@/components/empty/index.vue';

interface PieDataItem {
  name: string;
  value: number;
  color: string;
  totalNum?: number;
  y2Value?: number;
}

const props = defineProps({
  pieData: {
    type: Array as () => any[],
    default: () => {
      return [];
    },
  },
});
const optionData = ref<any[]>([]);
let option: any;
const observer = ref<ResizeObserver>();

// 监听 pieData 变化，更新 optionData
watch(
  () => props.pieData,
  (newData: PieDataItem[]) => {
    if (Array.isArray(newData) && newData.length > 0) {
      optionData.value = newData.map((item: PieDataItem) => ({
        name: item.name,
        value: item.value,
        itemStyle: {
          color: item.color,
        },
      }));
      if (optionData.value.length > 0) {
        nextTick(() => {
          init();
        });
      }
    }
  },
  { immediate: true, deep: true }
);

const init = () => {
  // 构建3d饼状图
  const chartDom = document.getElementById('cityGreenLand-charts');
  let myChart = echarts.init(chartDom);
  // 传入数据生成 option
  option = getPie3D(optionData.value, 0.8);
  option.series.push({
    backgroundColor: 'transparent',
    name: 'pie2d',
    type: 'pie',
    label: {
      textStyle: {
        color: ' rgba(255,255,255,0.85)',
      },
      formatter: '{b} {c}',
      opacity: 1,
      overflow: 'none',
    },
    itemStyle: { opacity: 0 }, // 完全透明
    labelLine: {
      show: true,
      lineStyle: {
        color: '#FFFFFF',
        width: 1,
      },
      length: 20,
      length2: 40,
    },
    startAngle: -60,
    clockwise: false,
    radius: ['60%', '50%'],
    center: ['50%', '30%'],
    data: optionData.value,
    tooltip: {
      show: false,
    },
  });

  myChart.setOption(option);

  // 正确使用 useEchartsResizeObserver
  const chartRef = ref(myChart as any);
  const parentRef = ref((chartDom?.parentElement || chartDom) as Element);
  observer.value = useEchartsResizeObserver(chartRef, parentRef).observer;

  bindListen(myChart);
};

const getPie3D = (pieData: any, internalDiameterRatio: any) => {
  //internalDiameterRatio:透明的空心占比
  let series: any = [];
  let sumValue = 0;
  let startValue = 0;
  let endValue = 10;
  let legendData: any = [];
  let legendBfb: any = [];
  let k = 1 - internalDiameterRatio;
  pieData.sort((a: any, b: any) => {
    return b.value - a.value;
  });
  // 为每一个饼图数据，生成一个 series-surface 配置
  for (let i = 0; i < pieData.length; i++) {
    sumValue += pieData[i].value;
    let seriesItem: any = {
      name: typeof pieData[i].name === 'undefined' ? `series${i}` : pieData[i].name,
      type: 'surface',
      parametric: true,
      radar: {
        shape: 'polygon',
        splitNumber: 0,
      },
      wireframe: {
        show: false,
      },
      pieData: pieData[i],
      pieStatus: {
        selected: false,
        hovered: true,
        k: k,
      },
      center: ['10%', '10%'],
    };

    if (typeof pieData[i].itemStyle != 'undefined') {
      let itemStyle: any = {};

      // 处理颜色，确保3D图形兼容性
      if (typeof pieData[i].itemStyle.color != 'undefined') {
        itemStyle.color = process3DColor(pieData[i].itemStyle.color);
      }

      typeof pieData[i].itemStyle.opacity != 'undefined' ? (itemStyle.opacity = pieData[i].itemStyle.opacity) : null;
      seriesItem.itemStyle = itemStyle;
    }
    series.push(seriesItem);
  }

  // 使用上一次遍历时，计算出的数据和 sumValue，调用 getParametricEquation 函数，
  // 向每个 series-surface 传入不同的参数方程 series-surface.parametricEquation，也就是实现每一个扇形。
  legendData = [];
  legendBfb = [];
  for (let i = 0; i < series.length; i++) {
    endValue = startValue + series[i].pieData.value;
    series[i].pieData.startRatio = startValue / sumValue;
    series[i].pieData.endRatio = endValue / sumValue;
    series[i].parametricEquation = getParametricEquation(
      series[i].pieData.startRatio,
      series[i].pieData.endRatio,
      false,
      false,
      k,
      series[i].pieData.value
    );
    startValue = endValue;
    let bfb = fomatFloat(series[i].pieData.value / sumValue, 4);
    legendData.push({
      name: series[i].name,
      value: bfb,
    });
    legendBfb.push({
      name: series[i].name,
      value: series[i].pieData.value,
    });
  }

  console.log(series);

  let boxHeight = getHeight3D(series, 16); //通过传参设定3d饼/环的高度，26代表26px
  // 准备待返回的配置项，把准备好的 legendData、series 传入。
  let option = {
    legend: [
      {
        data: legendData, // 第一行显示前两个
        orient: 'horizontal',
        left: 'center',
        bottom: 10,
        itemGap: 25,
        itemWidth: 12,
        itemHeight: 12,
        textStyle: {
          color: '#FFFFFF',
          fontSize: 12,
        },
        show: true,
        icon: 'rect',
        backgroundColor: 'rgba(66,80,108,0.15)',
        borderColor: 'rgba(67, 81, 108, 1)',
        borderWidth: 1,
        borderRadius: 1,
        padding: [8, 50],
        formatter: function (param: any) {
          let item = legendBfb.filter((item: any) => item.name == param)[0];
          let total = legendBfb.reduce((sum: number, item: any) => sum + item.value, 0);
          let percentage = Math.round((item.value / total) * 100);
          return `${item.name}  ${percentage}%`;
        },
      },
    ],

    tooltip: {
      formatter: (params: any) => {
        if (params.seriesName !== 'mouseoutSeries' && params.seriesName !== 'pie2d') {
          let bfb = (
            (option.series[params.seriesIndex].pieData.endRatio -
              option.series[params.seriesIndex].pieData.startRatio) *
            100
          ).toFixed(1);
          let value = option.series[params.seriesIndex].pieData.value;
          return (
            `<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${params.color};"></span>` +
            `${params.seriesName}<br/>` +
            `${value}<br />` +
            `${bfb}%`
          );
        }
      },
    },
    xAxis3D: {
      name: '',
      min: -1,
      max: 1,
    },
    yAxis3D: {
      name: '',
      min: -1,
      max: 1,
    },
    zAxis3D: {
      name: '',
      min: -1,
      max: 2,
    },
    grid3D: {
      top: -10,
      left: 40,
      show: true,
      boxHeight: boxHeight, //圆环的高度
      viewControl: {
        projection: 'orthographic',
        //3d效果可以放大、旋转等，请自己去查看官方配置
        alpha: 30, // X角度
        beta: 50, //Y角度
        distance: 100, //调整视角到主体的距离，类似调整zoom
        rotateSensitivity: 0, //设置为0无法旋转
        zoomSensitivity: 0, //设置为0无法缩放
        panSensitivity: 1, //设置为0无法平移
        autoRotate: false, //自动旋转
        center: [70, 10, 20], // 调节位置
      },
      splitLine: {
        //平面上的分隔线。
        show: false, //立体网格线
        // interval:100,//坐标轴刻度标签的显示间隔，在类目轴中有效
        splitArea: {
          show: true,
          // interval:100,//坐标轴刻度标签的显示间隔，在类目轴中有效
          areaStyle: {
            color: ['rgba(250,250,250,0.3)', 'rgba(200,200,200,0.3)', 'rgba(250,250,250,0.3)', 'rgba(200,200,200,0.3)'],
          },
        },
      },
      axisLabel: {
        show: false,
      },
      // axisLabel: {
      //     show: true,//是否显示刻度  (刻度上的数字，或者类目)
      //     //
      //     interval: 5,//坐标轴刻度标签的显示间隔，在类目轴中有效。
      //     formatter: function () {
      //         // return;
      //     },

      //     textStyle: {
      //         // color:'#000',//刻度标签样式，见图黑色刻度标签
      //         color: function (value) {
      //             return value >= 6 ? 'green' : 'red';//根据范围显示颜色，主页为值有效
      //         },
      //         //  borderWidth:"",//文字的描边宽度。
      //         //  borderColor:'',//文字的描边颜色。
      //         fontSize: 14,//刻度标签字体大小
      //         fontWeight: '',//粗细
      //     }
      // },
      axisTick: {
        show: false, //是否显示出刻度
        // interval:100,//坐标轴刻度标签的显示间隔，在类目轴中有效
        length: 5, //坐标轴刻度的长度
        lineStyle: {
          //举个例子，样式太丑将就
          color: '#000', //颜色
          opacity: 1,
          width: 5, //厚度（虽然为宽表现为高度），对应length*(宽)
        },
      },
      axisLine: {
        show: true,
        lineStyle: {
          opacity: 0,
        },
      },
      // splitLine: {//平面上的分隔线。
      //     show: true,//立体网格线
      //     // interval:100,//坐标轴刻度标签的显示间隔，在类目轴中有效
      //     splitArea: {
      //         show: true,
      //         // interval:100,//坐标轴刻度标签的显示间隔，在类目轴中有效
      //         areaStyle: {
      //             color: ['rgba(250,250,250,0.3)', 'rgba(200,200,200,0.3)', 'rgba(250,250,250,0.3)', 'rgba(200,200,200,0.3)']
      //         }
      //     },
      // },
      axisPointer: {
        //坐标轴指示线。
        show: false, //鼠标在chart上的显示线
        // lineStyle:{
        //     color:'#000',//颜色
        //     opacity:1,
        //     width:5//厚度（虽然为宽表现为高度），对应length*(宽)
        // }
      },
    },
    series: series,
  };
  return option;
};

//获取3d丙图的最高扇区的高度
const getHeight3D = (series: any, height: any) => {
  series.sort((a: any, b: any) => {
    return b.pieData.value - a.pieData.value;
  });
  return (height * 25) / series[0].pieData.value;
};

// 生成扇形的曲面参数方程，用于 series-surface.parametricEquation
const getParametricEquation = (startRatio: any, endRatio: any, isSelected: any, isHovered: any, k: any, h: any) => {
  // 计算
  let midRatio = (startRatio + endRatio) / 2;
  let startRadian = startRatio * Math.PI * 2;
  let endRadian = endRatio * Math.PI * 2;
  let midRadian = midRatio * Math.PI * 2;
  // 如果只有一个扇形，则不实现选中效果。
  if (startRatio === 0 && endRatio === 1) {
    isSelected = false;
  }
  // 通过扇形内径/外径的值，换算出辅助参数 k（默认值 1/3）
  k = typeof k !== 'undefined' ? k : 1 / 3;
  // 计算选中效果分别在 x 轴、y 轴方向上的位移（未选中，则位移均为 0）
  let offsetX = isSelected ? Math.cos(midRadian) * 0.1 : 0;
  let offsetY = isSelected ? Math.sin(midRadian) * 0.1 : 0;
  // 计算高亮效果的放大比例（未高亮，则比例为 1）
  let hoverRate = isHovered ? 1.05 : 1;
  // 返回曲面参数方程
  return {
    u: {
      min: -Math.PI,
      max: Math.PI * 3,
      step: Math.PI / 32,
    },
    v: {
      min: 0,
      max: Math.PI * 2,
      step: Math.PI / 20,
    },
    x: function (u: any, v: any) {
      if (u < startRadian) {
        return offsetX + Math.cos(startRadian) * (1 + Math.cos(v) * k) * hoverRate;
      }
      if (u > endRadian) {
        return offsetX + Math.cos(endRadian) * (1 + Math.cos(v) * k) * hoverRate;
      }
      return offsetX + Math.cos(u) * (1 + Math.cos(v) * k) * hoverRate;
    },
    y: function (u: any, v: any) {
      if (u < startRadian) {
        return offsetY + Math.sin(startRadian) * (1 + Math.cos(v) * k) * hoverRate;
      }
      if (u > endRadian) {
        return offsetY + Math.sin(endRadian) * (1 + Math.cos(v) * k) * hoverRate;
      }
      return offsetY + Math.sin(u) * (1 + Math.cos(v) * k) * hoverRate;
    },
    z: function (u: any, v: any) {
      if (u < -Math.PI * 0.5) {
        return Math.sin(u);
      }
      if (u > Math.PI * 2.5) {
        return Math.sin(u) * h * 0.1;
      }
      return Math.sin(v) > 0 ? 1 * h * 0.1 : -1;
    },
  };
};

const fomatFloat = (num: any, n: any) => {
  var f = parseFloat(num);
  if (isNaN(f)) {
    return false;
  }
  f = Math.round(num * Math.pow(10, n)) / Math.pow(10, n); // n 幂
  var s = f.toString();
  var rs = s.indexOf('.');
  //判定如果是整数，增加小数点再补0
  if (rs < 0) {
    rs = s.length;
    s += '.';
  }
  while (s.length <= rs + n) {
    s += '0';
  }
  return s;
};

// 处理3D图形颜色兼容性 - 支持渐变色
const process3DColor = (color: any) => {
  if (!color) return '#9B6FDC';

  // 如果是渐变对象，转换为3D兼容的渐变
  if (typeof color === 'object' && color.type === 'linear') {
    // 对于3D图形，我们使用渐变的中间色作为基础色
    if (color.colorStops && color.colorStops.length > 0) {
      // 取第一个颜色作为基础色，通过光照实现渐变效果
      return color.colorStops[0].color;
    }
  }

  // 如果是普通颜色字符串，直接返回
  return color;
};

const bindListen = (myChart: any) => {
  // 监听鼠标事件，实现饼图选中效果（单选），近似实现高亮（放大）效果。
  let selectedIndex = '';
  let hoveredIndex = '';
  // 监听点击事件，实现选中效果（单选）
  myChart.on('click', function (params: any) {
    // 从 option.series 中读取重新渲染扇形所需的参数，将是否选中取反。
    let isSelected = !option.series[params.seriesIndex].pieStatus.selected;
    let isHovered = option.series[params.seriesIndex].pieStatus.hovered;
    let k = option.series[params.seriesIndex].pieStatus.k;
    let startRatio = option.series[params.seriesIndex].pieData.startRatio;
    let endRatio = option.series[params.seriesIndex].pieData.endRatio;
    // 如果之前选中过其他扇形，将其取消选中（对 option 更新）
    if (selectedIndex !== '' && selectedIndex !== params.seriesIndex) {
      option.series[selectedIndex].parametricEquation = getParametricEquation(
        option.series[selectedIndex].pieData.startRatio,
        option.series[selectedIndex].pieData.endRatio,
        false,
        false,
        k,
        option.series[selectedIndex].pieData.value
      );
      option.series[selectedIndex].pieStatus.selected = false;
    }
    // 对当前点击的扇形，执行选中/取消选中操作（对 option 更新）
    option.series[params.seriesIndex].parametricEquation = getParametricEquation(
      startRatio,
      endRatio,
      isSelected,
      isHovered,
      k,
      option.series[params.seriesIndex].pieData.value
    );
    option.series[params.seriesIndex].pieStatus.selected = isSelected;
    // 如果本次是选中操作，记录上次选中的扇形对应的系列号 seriesIndex
    isSelected ? (selectedIndex = params.seriesIndex) : null;
    // 使用更新后的 option，渲染图表
    myChart.setOption(option);
  });
  // 监听 mouseover，近似实现高亮（放大）效果
  myChart.on('mouseover', function (params: any) {
    // 准备重新渲染扇形所需的参数
    let isSelected;
    let isHovered;
    let startRatio;
    let endRatio;
    let k;
    // 如果触发 mouseover 的扇形当前已高亮，则不做操作
    if (hoveredIndex === params.seriesIndex) {
      return;
      // 否则进行高亮及必要的取消高亮操作
    } else {
      // 如果当前有高亮的扇形，取消其高亮状态（对 option 更新）
      if (hoveredIndex !== '') {
        // 从 option.series 中读取重新渲染扇形所需的参数，将是否高亮设置为 false。
        isSelected = option.series[hoveredIndex].pieStatus.selected;
        isHovered = false;
        startRatio = option.series[hoveredIndex].pieData.startRatio;
        endRatio = option.series[hoveredIndex].pieData.endRatio;
        k = option.series[hoveredIndex].pieStatus.k;
        // 对当前点击的扇形，执行取消高亮操作（对 option 更新）
        option.series[hoveredIndex].parametricEquation = getParametricEquation(
          startRatio,
          endRatio,
          isSelected,
          isHovered,
          k,
          option.series[hoveredIndex].pieData.value
        );
        option.series[hoveredIndex].pieStatus.hovered = isHovered;
        // 将此前记录的上次选中的扇形对应的系列号 seriesIndex 清空
        hoveredIndex = '';
      }
      // 如果触发 mouseover 的扇形不是透明圆环，将其高亮（对 option 更新）
      if (params.seriesName !== 'mouseoutSeries' && params.seriesName !== 'pie2d') {
        // 从 option.series 中读取重新渲染扇形所需的参数，将是否高亮设置为 true。
        isSelected = option.series[params.seriesIndex].pieStatus.selected;
        isHovered = true;
        startRatio = option.series[params.seriesIndex].pieData.startRatio;
        endRatio = option.series[params.seriesIndex].pieData.endRatio;
        k = option.series[params.seriesIndex].pieStatus.k;
        // 对当前点击的扇形，执行高亮操作（对 option 更新）
        option.series[params.seriesIndex].parametricEquation = getParametricEquation(
          startRatio,
          endRatio,
          isSelected,
          isHovered,
          k,
          option.series[params.seriesIndex].pieData.value + 5
        );
        option.series[params.seriesIndex].pieStatus.hovered = isHovered;
        // 记录上次高亮的扇形对应的系列号 seriesIndex
        hoveredIndex = params.seriesIndex;
      }
      // 使用更新后的 option，渲染图表
      myChart.setOption(option);
    }
  });
  // 修正取消高亮失败的 bug
  myChart.on('globalout', function () {
    // 准备重新渲染扇形所需的参数
    let isSelected;
    let isHovered;
    let startRatio;
    let endRatio;
    let k;
    if (hoveredIndex !== '') {
      // 从 option.series 中读取重新渲染扇形所需的参数，将是否高亮设置为 true。
      isSelected = option.series[hoveredIndex].pieStatus.selected;
      isHovered = false;
      k = option.series[hoveredIndex].pieStatus.k;
      startRatio = option.series[hoveredIndex].pieData.startRatio;
      endRatio = option.series[hoveredIndex].pieData.endRatio;
      // 对当前点击的扇形，执行取消高亮操作（对 option 更新）
      option.series[hoveredIndex].parametricEquation = getParametricEquation(
        startRatio,
        endRatio,
        isSelected,
        isHovered,
        k,
        option.series[hoveredIndex].pieData.value
      );
      option.series[hoveredIndex].pieStatus.hovered = isHovered;
      // 将此前记录的上次选中的扇形对应的系列号 seriesIndex 清空
      hoveredIndex = '';
    }
    // 使用更新后的 option，渲染图表
    myChart.setOption(option);
  });
};

onMounted(() => {});

onUnmounted(() => {
  // 清理 ResizeObserver
  if (observer.value) {
    observer.value.disconnect();
  }
});
</script>

<style lang="scss" module>
.waterEvalContainer {
  width: 100%;
  height: 100%;
}
.cityGreenLandCharts {
  width: 100%;
  height: 100%;
}
</style>
