<template>
  <div :class="$style['list-wrapper']">
    <n-collapse display-directive="show" :default-expanded-names="expandedNames">
      <template #header-extra="{ collapsed }">
        <n-icon size="16" color="#B4DDFB">
          <component :is="collapsed ? ArrowUp : ArrowDown"></component>
        </n-icon>
      </template>
      <template #arrow>
        <span></span>
      </template>
      <n-collapse-item v-for="floorItem in listData" :key="floorItem.floorId" :name="floorItem.floorId">
        <template #header>
          <div :class="$style['header-box']">
            <span :class="$style['header-icon']"></span>
            <span :class="$style['header-title']">{{ floorItem.floorName }}</span>
          </div>
        </template>
        <div class="pl-[10px]">
          <ItemComp
            v-for="deviceItem in floorItem.deviceList"
            :key="deviceItem.videoId"
            :class="$style['inspect-card']"
            :info="deviceItem"
            @event-handle="eventHandle"
          />
        </div>
      </n-collapse-item>
    </n-collapse>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { AkChevronUp as ArrowUp, AkChevronDown as ArrowDown } from '@kalimahapps/vue-icons';
import ItemComp from './ViewMapInspectItem.vue';
import { ITaskInspectListByFloorItem } from '../type';

defineOptions({ name: 'ViewMapInspectList' });

interface IProps {
  listData: ITaskInspectListByFloorItem[];
}
const props = withDefaults(defineProps<IProps>(), {
  listData: () => [],
});

const emits = defineEmits(['event-handle']);
const eventHandle = (val) => {
  emits('event-handle', val);
};

const expandedNames = ref<string[]>([]);

watch(
  () => props.listData,
  (nv) => {
    if (nv.length > 0) {
      nv.forEach((item) => {
        expandedNames.value.push(item.floorId);
      });
    }
  }
);
</script>

<style module lang="scss">
.list-wrapper {
  width: 100%;
  padding: 0 20px 10px 20px;
}

.header-box {
  @apply flex flex-row items-center gap-[8px];
  font-size: 18px;
  line-height: 1;
  .header-icon {
    width: 16px;
    height: 16px;
    background: url('../assets/building.png') center no-repeat;
  }
}
</style>
