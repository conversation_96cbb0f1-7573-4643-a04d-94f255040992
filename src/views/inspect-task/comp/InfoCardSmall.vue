<template>
  <div :class="$style.InspectTaskInfoCardTask">
    <div
      v-for="(item, index) of cards"
      :key="item.label"
      :class="[$style.card, $style[`bg-${index + 1}`], curCardIndex === index && $style.active]"
      @click="handleCardClick(item, index)"
    >
      <div>{{ item.value }}{{ item.suffix || '' }}</div>
      <div>{{ item.label }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { getTaskInspectionDetailStatistic } from '../fetchData.ts';
import { ITaskInspectionDetailStatistic } from '@/views/inspect-task/type.ts';
import { useRoute } from 'vue-router';

const route = useRoute();

const emits = defineEmits(['change']);

interface CardItem {
  label: string;
  value: number | string;
  key: keyof ITaskInspectionDetailStatistic;
  inspectionStatus: string;
  suffix?: string; // 后缀，如 '%'
}

// 巡检状态: 0-未巡检; 1-已巡检; 2-异常

const cards = ref<CardItem[]>([
  { label: '已巡检（个）', value: 0, key: 'finishedNum', inspectionStatus: '1' },
  { label: '待巡检（个）', value: 0, key: 'waitOpenNum', inspectionStatus: '0' },
  { label: '异常事件（个）', value: 0, key: 'hazardNum', inspectionStatus: '2' },
  { label: '巡检进度', value: 0, key: 'progress', suffix: '%', inspectionStatus: '' },
]);

// 当前卡片索引
const curCardIndex = ref<number | null>(null);

function handleCardClick(item: CardItem, index: number) {
  if (index === 3) return; // 最后一个不需要选中

  if (curCardIndex.value === index) {
    curCardIndex.value = null;
  } else {
    curCardIndex.value = index;
  }

  emits('change', { inspectionStatus: curCardIndex.value === null ? null : item.inspectionStatus });
}

function getData() {
  getTaskInspectionDetailStatistic({
    taskId: route.query.taskId,
  }).then((res) => {
    const data = res.data || {};
    // 按key映射值
    cards.value = cards.value.map((c) => {
      const value = Number(data[c.key] ?? 0);
      return { ...c, value };
    });
  });
}

// init
getData();

defineOptions({ name: 'InspectTaskInfoCardSmall' });
</script>

<style module lang="scss">
.InspectTaskInfoCardTask {
  display: grid;
  grid-template-columns: repeat(auto-fill, 144px);
  gap: 10px 20px;

  .card {
    width: 144px;
    height: 74px;
    display: flex;
    flex-direction: column;
    padding: 5px 20px;
    color: #fff;
    background-size: cover;
    background-position: center;
    border-radius: 2px;
    cursor: pointer;
    &.bg-1 {
      background-image: url('../assets/bg-sm-c1.png');
      &.active {
        filter: glow-shadow(#00db42);
      }
    }
    &.bg-2 {
      background-image: url('../assets/bg-sm-c2.png');
      &.active {
        filter: glow-shadow(#ff8229);
      }
    }
    &.bg-3 {
      background-image: url('../assets/bg-sm-c3.png');
      &.active {
        filter: glow-shadow(#ff6767);
      }
    }
    &.bg-4 {
      background-image: url('../assets/bg-sm-c4.png');
      cursor: not-allowed;
    }

    > div:nth-child(1) {
      font-size: 24px;
      font-weight: bold;
      margin-bottom: 4px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      max-width: 100%;
    }

    > div:nth-child(2) {
      font-size: 14px;
      font-weight: bold;
      opacity: 0.9;
    }
  }
}
</style>
