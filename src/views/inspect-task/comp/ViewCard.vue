<template>
  <div :class="[$style.InspectTaskViewCard, 'com-g-row-a1']">
    <div>
      <Cards @change="handleInfoCardTaskChange" />
      <Filter ref="filterRef" @action="actionFn" />
    </div>

    <div class="com-g-row-1a !min-h-[400px]">
      <ComBoxGroup v-if="dataList.length">
        <ComBoxA
          v-for="(data, i) of dataList"
          :key="data.id"
          :status-label="getVideoResultLabel(String(data.videoResult))"
          :status-bg-class="VIDEO_RESULT_STATUS_CLASS_MAP[data.videoResult]"
        >
          <Item :data="data" :index="i" @show-detail-dia="handleShowDetailDia" />
        </ComBoxA>
      </ComBoxGroup>
      <Empty title="" v-else />
      <PaginationComp class="justify-end items-center mt-[15px]" />
    </div>

    <!--  弹窗  -->
    <InspectInfoDia v-model:show="showDia" :data="inspectInfoDiaData" />
  </div>
</template>

<script lang="ts" setup>
import { IObj } from '@/types';
import { IActionData, ITaskInspectionDetailPageItem } from '../type.ts';
import { getTaskInspectionDetailPageList as pageList } from '../fetchData.ts';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useNaivePagination, useNPaginationComp } from '@/common/hooks/useNaivePagination.ts';
import { ref, computed } from 'vue';
import ComBoxA from '@/components/box/ComBoxA.vue';
import ComBoxGroup from '@/components/box/ComBoxGroup.vue';
import Item from './ViewCardItem.vue';
import Cards from './InfoCardSmall.vue';
import Filter from './ViewCardFilter.vue';
import InspectInfoDia from '@/views/inspect-com/InspectInfoDia.vue';
import { ACTION } from '@/views/inspect-task/constant.ts';
import { VIDEO_RESULT_STATUS_CLASS_MAP } from '@/views/common/contant.ts';
import Empty from '@/components/empty/index.vue';
import { frontendDict } from '@/views/common/dict/frontendDict.ts';

const emits = defineEmits(['action']);

const [loading, search] = useAutoLoading(true);
const dataList = ref<ITaskInspectionDetailPageItem[]>([]);
const { pagination, updateTotal } = useNaivePagination(getData, { pageSizes: [6, 12, 18], pageSize: 6 });
const PaginationComp = useNPaginationComp(pagination);
const showDia = ref(false);
const inspectInfoDiaData = ref<IObj<any>>({});
const filterRef = ref();

// 通过字典值映射名称
const { xjspjgOpt } = frontendDict.useXjspjg();
const xjspjgMap = computed<Record<string, string>>(() => {
  const map: Record<string, string> = {};
  (xjspjgOpt.value || []).forEach((item) => {
    map[item.dictValue] = item.dictLabel;
  });
  return map;
});
function getVideoResultLabel(val: string) {
  return xjspjgMap.value[val] ?? '--';
}

let filterData: IObj<any> = {}; // 搜索条件

function actionFn(val: IActionData) {
  if (val.action === ACTION.SEARCH) {
    getDataWrap(val.data);
  }
}

function handleShowDetailDia(data: any) {
  showDia.value = true;
  inspectInfoDiaData.value = data;
}

function getData() {
  const params = {
    pageNo: pagination.page,
    pageSize: pagination.pageSize,
    ...filterData,
  };

  search(pageList(params)).then((res) => {
    // res.data.rows[0].videoDesc = '20250815/7c9ea6ef5f6a481f876d6c7fde5bd7d5.jpg';
    dataList.value = res.data.rows || [];
    updateTotal(res.data.total || 0);
  });
}

function getDataWrap(data: IObj<any>) {
  filterData = Object.assign({}, data) || {};
  pagination.page = 1;
  getData();
}
function handleInfoCardTaskChange(data: IObj<any>) {
  filterRef.value?.emit(data, 'InfoCardTask');
}

defineExpose({
  getDataWrap,
  getData,
});

defineOptions({ name: 'InspectTaskViewCard' });
</script>

<style module lang="scss">
.InspectTaskViewCard {
  width: 100%;
  height: 100%;
  row-gap: 20px;
  // padding: 0 0 20px;
}
</style>
