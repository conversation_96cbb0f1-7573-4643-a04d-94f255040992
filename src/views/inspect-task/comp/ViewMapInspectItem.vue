<template>
  <div :class="$style['inspect-item']">
    <div :class="$style['header']">
      <span :class="$style['index']">{{ info.videoSort }}.</span>
      <n-ellipsis :class="$style['title']">
        {{ info.deviceAddress || '--' }}
      </n-ellipsis>
      <span :class="[$style['status'], $style[getStatusClass(info.videoResult)]]">
        {{ info.videoResultName || '--' }}
      </span>
    </div>

    <!-- <div v-if="info.videoResult == 2 && info.eventTypeNames" :class="$style['eventNames']">
      <n-ellipsis>
        <span>异常事件：</span>
        {{ info.eventTypeNames }}
      </n-ellipsis>
    </div> -->

    <div v-if="info.videoResult == 2 && info.eventTypeNames">
      <span class="text-[#fff]">异常事件：</span>
      <div :class="$style['eventList']">
        <span
          :class="$style['eventNames']"
          v-for="item in eventList"
          :key="item.disposeId"
          @click="eventHandle(item)"
          >{{ item.eventTypeName }}</span
        >
      </div>
    </div>

    <div :class="$style.time">巡检时间：{{ info.videoTime || '--' }}</div>

    <div v-if="info.videoUrl" :class="$style.imageWrap">
      <n-image
        class="w-full h-full"
        :render-toolbar="renderCustomToolbar"
        :src="getFullThumbnailUrl(info.videoUrl, '135x80')"
        :preview-src="getFullFileUrl(info.videoUrl)"
        alt=""
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { STATUS_COLORS, VIDEO_RESULT_STATUS_CLASS_MAP } from '@/views/common/contant.ts';
import { getFullFileUrl, getFullThumbnailUrl } from '@/utils/fileUrl.ts';
import { renderCustomToolbar } from '@/common/hooks/useImagePreview.ts';
import { ITaskInspectionDetailPageItem } from '../type';

defineOptions({ name: 'ViewMapInspectItem' });

interface IProps {
  info: Partial<ITaskInspectionDetailPageItem>;
}

const props = withDefaults(defineProps<IProps>(), {
  info: () => ({}),
});

const eventList = computed(() => {
  return props.info.disposeList?.filter((item) => item.videoResult === 2) || [];
});

const getStatusClass = (videoResult: number | undefined) => {
  const _res = videoResult || 0;
  return VIDEO_RESULT_STATUS_CLASS_MAP[_res];
};

const emits = defineEmits(['event-handle']);
const eventHandle = (data: any) => {
  if (!data.disposeId) return;
  emits('event-handle', data);
};
</script>

<style module lang="scss">
.inspect-item {
  @apply flex flex-col gap-[10px] mb-[20px] text-[14px];
  line-height: 20px;
  color: var(--skin-t1);
}

.header {
  @apply flex flex-row flex-nowrap items-center;

  .title {
    @apply flex-1 ml-[4px] mr-[8px];
  }

  .status {
    min-width: 44px;
    padding: 0 vw(5);
    height: 20px;
    line-height: 20px;
    color: #fff;
    font-size: 12px;
    border-radius: 4px;
    text-align: center;
    cursor: pointer;

    &.yellow {
      background: v-bind('STATUS_COLORS.yellow');
    }

    &.green {
      background: v-bind('STATUS_COLORS.green');
    }

    &.red {
      background: v-bind('STATUS_COLORS.red');
    }
  }
}

.eventList {
  display: flex;
  flex-flow: row wrap;
  gap: 8px;
}
.eventNames {
  cursor: pointer;
  padding: 4px 0;
  color: #fa5151;
  border-bottom: 1px solid #fa5151;
}

.time {
  width: 100%;
  height: 28px;
  line-height: 28px;
  font-size: 14px;
  padding: 0 7px;
  backdrop-filter: blur(3px);
}

.imageWrap {
  display: flex;
  justify-content: center;
  width: 135px;
  height: 80px;

  img {
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.15);
  }
}
</style>
