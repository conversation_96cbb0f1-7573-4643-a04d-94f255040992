<template>
  <div :class="$style['wrap']">
    <n-form :show-feedback="false" label-placement="left">
      <n-grid class="flex-1" :cols="18" :x-gap="30" :y-gap="20">
        <n-form-item-gi :span="3" label="楼层">
          <n-select
            v-model:value="filterForm.floorId"
            :options="floorOpt"
            label-field="floorName"
            value-field="floorId"
            clearable
            placeholder="全部"
          />
        </n-form-item-gi>

        <n-form-item-gi label="在线状态" :span="3">
          <n-select
            v-model:value="filterForm.onlineState"
            :options="onlineStateOpt"
            label-field="dictLabel"
            value-field="dictValue"
            clearable
            placeholder="全部"
          />
        </n-form-item-gi>

        <n-form-item-gi :span="4" label="设备位置">
          <n-input v-model:value="filterForm.addressLike" maxlength="50" clearable placeholder="请输入设备位置" />
        </n-form-item-gi>
      </n-grid>
    </n-form>
  </div>
</template>

<script lang="ts" setup>
import { $dict } from '@/views/common/dict/dict.ts';
import { ACTION } from '../constant';
import { EventSvc } from '../EventSvc.ts';
import { getFloorList } from '../fetchData.ts';
import { onBeforeUnmount, onMounted, ref, watch } from 'vue';
import { trimObjNull } from '@/utils/obj.ts';
import { useRoute } from 'vue-router';
import { IObj } from '@/types';

const emits = defineEmits(['action']);
const route = useRoute();
const filterForm = ref(initForm());
const floorOpt = ref<{ floorId: string; floorName: string }[]>([]);
const { onlineStateOpt } = $dict.useOnlineState();

function initForm() {
  return {
    taskId: route.query.taskId,
    floorId: null, // 楼层id
    onlineState: null, // 在线状态
    addressLike: '', // 位置模糊搜索
    inspectionStatus: '',
  };
}

function getFilterForm() {
  return trimObjNull(Object.assign({}, filterForm.value));
}

function doHandle(action: ACTION, extData?: IObj<any>) {
  emits('action', {
    action: action,
    data: Object.assign(getFilterForm(), trimObjNull(extData || {})),
  });
}

const taskTopDetailSub = EventSvc.taskTopDetail$.subscribe((res) => {
  if (!res.buildingId) return;

  getFloorList({
    unitId: res.erecordUnitId,
    buildId: res.buildingId,
  }).then((res) => {
    floorOpt.value = res.data || [];
  });
});

onBeforeUnmount(() => {
  taskTopDetailSub.unsubscribe();
});

onMounted(() => {
  doHandle(ACTION.SEARCH);
});

watch(
  () => filterForm.value,
  () => {
    doHandle(ACTION.SEARCH);
  },
  { deep: true }
);

defineExpose({
  emit(data: IObj<any>) {
    console.log('data', data);
    // doHandle(ACTION.SEARCH, data);
    filterForm.value.inspectionStatus = data.inspectionStatus;
  },
});

defineOptions({ name: 'InspectTaskViewCardFilter' });
</script>

<style module lang="scss">
.wrap {
  width: 100%;
  padding: 20px 0;
}
</style>
