<template>
  <div :class="$style.InspectTaskList" class="com-g-row-1a">
    <ComBoxGroup v-if="dataList.length">
      <ComBoxA
        v-for="data of dataList"
        :key="data.taskId"
        :status-label="data.taskStatusName"
        :status-bg-class="TASK_STATUS_CLASS_MAP[data.taskStatus]"
      >
        <Item :data="data" @to-detail="handleToDetail" />
      </ComBoxA>
    </ComBoxGroup>
    <Empty title="" v-else />
    <PaginationComp class="justify-end items-center" />
  </div>
</template>

<script lang="ts" setup>
import ComBoxA from '@/components/box/ComBoxA.vue';
import ComBoxGroup from '@/components/box/ComBoxGroup.vue';
import Empty from '@/components/empty/index.vue';
import Item from './Item.vue';
import { IObj } from '@/types';
import { IPageItem } from '../type.ts';
import { pageList } from '../fetchData.ts';
import { inject, ref } from 'vue';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useNaivePagination, useNPaginationComp } from '@/common/hooks/useNaivePagination.ts';
import { useRouter } from 'vue-router';
import { TASK_STATUS_CLASS_MAP } from '@/views/common/contant';
import { PROVIDE_KEY } from '@/views/inspect-task/constant.ts';

const emits = defineEmits(['action']);

const router = useRouter();
const fromPlanPage = inject(PROVIDE_KEY.fromPlanPage);

const [loading, search] = useAutoLoading(true);
const dataList = ref<IPageItem[]>([]);
const { pagination, updateTotal } = useNaivePagination(getData, {
  pageSizes: [6, 12, 18],
  pageSize: 6,
});
const PaginationComp = useNPaginationComp(pagination);

let filterData: IObj<any> = {}; // 搜索条件

function handleToDetail(data: IObj<any>) {
  router.push({
    name: 'inspectTaskDetail',
    query: {
      taskId: data.taskId,
      planId: data.planId,
      unitId: data.deptId || filterData.unitId,
      from: fromPlanPage ? 'plan' : undefined,
    },
  });
}

function getData() {
  const params = {
    pageNo: pagination.page,
    pageSize: pagination.pageSize,
    ...filterData,
  };

  search(pageList(params))
    .then((res) => {
      dataList.value = res.data.rows || [];
      updateTotal(res.data.total || 0);
    })
    .catch(() => {
      dataList.value = [];
    });
}

function getDataWrap(data: IObj<any>) {
  filterData = Object.assign({}, data) || {};
  pagination.page = 1;
  getData();
}

defineExpose({
  getDataWrap,
  getData,
});

defineOptions({ name: 'InspectTaskList' });
</script>

<style module lang="scss">
.InspectTaskList {
  row-gap: 20px;
  padding: 10px;
}
</style>
yellow
