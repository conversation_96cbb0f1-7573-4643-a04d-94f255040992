c
<template>
  <ComDialogC title="视频点位巡检详情" :width="848" :height="410" :close-on-esc="false">
    <template #header-r>
      <n-flex :size="[24, 0]" class="text-[14px]" v-if="hasStep">
        <n-button text title="上一条" :disabled="loading || curPositionNo === 1" @click="handlePrev">
          <template #icon>
            <n-icon :size="26"><IconArrow class="w-[20px]" /></n-icon>
          </template>
        </n-button>

        <n-button text title="下一条" :disabled="loading || curPositionNo === data.deviceTotal" @click="handleNext">
          <template #icon>
            <n-icon :size="26"><IconArrow class="rotate-180 w-[20px]" /></n-icon>
          </template>
        </n-button>
      </n-flex>
    </template>

    <div :class="$style.content" class="com-g-col-a1 gap-x-[15px]">
      <n-scrollbar :class="$style.l">
        <div class="font-bold text-[16px] mb-[5px]">巡检基本信息</div>
        <div>
          <p>巡检时间：{{ detail.videoTime || '--' }}</p>
          <p class="flex">
            <span class="shrink-0">楼栋楼层：</span>
            <n-ellipsis line-clamp="1">
              {{ (detail?.buildingName || '') + (detail?.floorName || '') || '--' }}
            </n-ellipsis>
          </p>
          <p class="flex">
            <span class="shrink-0">设备位置：</span>
            <n-ellipsis line-clamp="1">{{ detail?.deviceAddress || '--' }}</n-ellipsis>
          </p>
          <p class="flex">
            <span class="shrink-0">品牌型号：</span>
            <n-ellipsis line-clamp="1">
              {{ (detail?.brand || '') + (detail?.model || '') || '--' }}
            </n-ellipsis>
          </p>
          <p class="flex">
            <span class="shrink-0">设备编号：</span>
            <n-ellipsis line-clamp="1">{{ detail?.deviceId || '--' }}</n-ellipsis>
          </p>
        </div>

        <div class="font-bold text-[16px] mb-[5px] mt-[20px]">巡检结果</div>
        <template v-if="(detail.inspectionResult || [])?.length">
          <div :class="$style.result" v-for="item of detail.inspectionResult" :key="item.id">
            <div v-if="item.videoResult === 2">
              <span>{{ item.eventTypeName }}：</span>
              <div :class="[$style.btn, $style.error]" title="查看" @click="handleShowDrawer(item)">
                {{ item.videoResultName || '--' }}{{ item.videoResultName ? ' >>' : '' }}
              </div>
            </div>
            <!--      只需要展示 正常、异常、--        -->
            <!--              <div v-else-if="item.videoResult === 1">-->
            <!--                <span>{{ item.eventTypeName }}：</span>-->
            <!--                <div :class="[$style.btn, $style.un]">{{ item.videoResultName || '&#45;&#45;' }}</div>-->
            <!--              </div>-->
            <div v-else-if="item.videoResult === 0">
              <span>{{ item.eventTypeName }}：</span>
              <div :class="[$style.btn, $style.ok]">{{ item.videoResultName || '--' }}</div>
            </div>
            <div v-else>
              <span>{{ item.eventTypeName }}：</span>
              <div :class="[$style.btn]">{{ '--' }}</div>
            </div>
          </div>
        </template>
        <div v-else>--</div>
      </n-scrollbar>

      <div :class="$style.r">
        <div v-if="detail.videoUrl" :class="$style.imageWrap">
          <n-image
            class="w-full h-full"
            :render-toolbar="renderCustomToolbar"
            :src="getFullThumbnailUrl(detail.videoUrl, '560x315')"
            :preview-src="getFullFileUrl(detail.videoUrl)"
            alt=""
          />
        </div>
        <div v-else :class="$style.imageEmptyWrap">
          <img src="./assets/card-no-pic.png" alt="" />
          <p v-if="!detail.videoTime">任务开始后自动拍摄巡检截图</p>
        </div>
      </div>

      <div :class="$style.videoBtn">
        <n-button type="primary" @click="showVideoDia = true" :disabled="!detail.deviceId">查看视频</n-button>
      </div>

      <div :class="$style.loading" v-if="loading">
        <n-spin :show="true" />
      </div>
    </div>

    <!-- 叠加弹窗 -->
    <VideoDia v-model:show="showVideoDia" :device-id="detail.deviceId || ''" />
    <InspectInfoDrawer v-model:show="showDrawer" :data="curDisposeData" />
  </ComDialogC>
</template>

<script setup lang="ts">
import ComDialogC from '@/components/dialog/ComDialogC.vue';
import InspectInfoDrawer from './InspectInfoDrawer.vue';
import VideoDia from './InspectVideoDia.vue';
import { getFullFileUrl, getFullThumbnailUrl } from '@/utils/fileUrl.ts';
import { getTaskPointDetail } from '@/views/inspect-com/fetchData.ts';
import { IObj } from '@/types';
import { ITaskPointDetailVo, InspectionVideoTaskDispose } from './ITaskPointDetailVo.ts';
import { MdExpandLess as IconArrow } from '@kalimahapps/vue-icons';
import { computed, ref, useAttrs, watch } from 'vue';
import { renderCustomToolbar } from '@/common/hooks/useImagePreview.ts';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useRoute } from 'vue-router';

const route = useRoute();
const $attr = useAttrs();

const props = defineProps({
  data: {
    type: Object as () => Partial<{ deviceTotal: number; positionNo: number; [key: string]: any }>,
    default: () => {},
  },
});

const [loading, wrapFn] = useAutoLoading(false, 500);
const showDrawer = ref(false);
const showVideoDia = ref(false);
const detail = ref<Partial<ITaskPointDetailVo>>({});
const curDisposeData = ref<IObj<any>>({});
const curPositionNo = ref(1); // 当前 positionNo 位置， 从1起
const hasStep = computed(() => {
  return Number(props.data?.deviceTotal) > 1;
});

function handlePrev() {
  if (curPositionNo.value > 1) {
    curPositionNo.value -= 1;
  } else {
    curPositionNo.value = 1;
  }

  getData();
}

function handleNext() {
  const total = props.data.deviceTotal || 1;
  if (curPositionNo.value < total) {
    curPositionNo.value += 1;
  } else {
    curPositionNo.value = total;
  }

  getData();
}

function handleShowDrawer(val: InspectionVideoTaskDispose) {
  curDisposeData.value = {
    disposeId: val.disposeId,
    disposeEventType: val.disposeEventType,
  };
  showDrawer.value = true;
}

function getData() {
  const params = {
    taskId: route.query.taskId,
    positionNo: curPositionNo.value,
  };

  wrapFn(getTaskPointDetail(params))
    .then((res) => {
      // 调试代码 ->
      // if (import.meta.env.DEV) {
      //   // if (!res.data.inspectionResult?.length) {
      //   res.data.inspectionResult = [
      //     {
      //       id: '1',
      //       videoResult: 2,
      //       disposeId: '53853c661f023078cfdb691001e94014',
      //       eventTypeName: '测试1-火警',
      //       videoResultName: '异常',
      //       disposeEventType: 1,
      //     },
      //     {
      //       id: '1',
      //       videoResult: 2,
      //       disposeId: 'e56a2aa3d32822201420ef6a9aeab4c0',
      //       eventTypeName: '测试2-隐患',
      //       videoResultName: '异常',
      //       disposeEventType: 4,
      //     },
      //     { id: '2', videoResult: 1, disposeId: '1', eventTypeName: '测试3', videoResultName: '正常' },
      //   ];
      //   // }
      //   if (!res.data.videoUrl) {
      //     res.data.videoUrl = '20250815/7c9ea6ef5f6a481f876d6c7fde5bd7d5.jpg';
      //   }
      // }
      // 调试代码 <-

      detail.value = res.data || {};
    })
    .catch(() => {
      detail.value = {};
    });
}

function reset() {
  detail.value = {};
  curDisposeData.value = {};
  curPositionNo.value = 1;
}

watch(
  () => $attr.show,
  (val) => {
    if (val) {
      curPositionNo.value = props.data.positionNo || 1;
      getData();
    } else {
      reset();
    }
  }
);

defineOptions({ name: 'InspectInfoDia' });
</script>

<style module lang="scss">
.content {
  position: relative;
  backdrop-filter: blur(3px);

  .l {
    width: 225px;
    height: 270px;
    line-height: 1.6;

    .result {
      line-height: 1.7;
      padding-right: 10px;

      & > div {
        display: flex;
        justify-content: space-between;
      }

      .btn {
        min-width: 32px;
        height: 17px;
        line-height: 17px;
        font-size: 12px;
        border-radius: 4px;
        text-align: center;
        padding: 0 3px;
      }

      .ok {
        background: #15753b;
      }

      .error {
        background: #f32e2e;
        cursor: pointer;
      }

      .un {
        background: #d38415;
      }
    }
  }

  .r {
    aspect-ratio: 16/9;
    background: rgba(0, 0, 0, 0.1);

    .imageWrap {
      width: 100%;
      height: 100%;
      overflow: hidden;
    }

    .imageEmptyWrap {
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      font-size: 14px;
      color: #6b7b99;
      user-select: none;
      pointer-events: none;
    }
  }

  .loading {
    position: absolute;
    top: 30%;
    left: 112.5px;
    transform: translate(-50%, -50%);
  }

  .videoBtn {
    position: absolute;
    bottom: 0;
    left: 0;
  }
}
</style>
