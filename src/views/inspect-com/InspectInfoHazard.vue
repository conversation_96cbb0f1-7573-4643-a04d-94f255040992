<template>
  <div :class="$style.InspectInfoHazard">
    <ComBoxB title="隐患信息" :is-empty="false">
      <div>
        <p>隐患来源：{{ detail.hazardSourceName }}</p>
        <p>隐患类别：{{ detail.hazardTypeName || '其他隐患' }}</p>
        <p>隐患描述：{{ detail.hazardDesc || '--' }}</p>
        <template v-if="detail.hazardSource == 2">
          <p>上报人员：{{ detail.operatorName || '--' }}</p>
          <div class="com-g-col-a1">
            <p>现场照片：</p>
            <n-image-group :render-toolbar="renderCustomToolbar">
              <div :class="$style.imageGroup">
                <n-image
                  v-for="item of detail.fireDisposeAttchList"
                  :key="item"
                  :src="getFullThumbnailUrl(item, '80x64')"
                  :preview-src="getFullFileUrl(item)"
                  alt=""
                />
              </div>
            </n-image-group>
          </div>
        </template>
      </div>
    </ComBoxB>

    <!--    <ComBoxB title="异常记录" :is-empty="false" v-if="detail.problemDesc == '管网压力异常'">-->
    <!--      <div>todo</div>-->
    <!--    </ComBoxB>-->

    <ComBoxB title="设备信息" :is-empty="false">
      <div>
        <p>单位名称：{{ detail?.deviceInfo?.unitName || '--' }}</p>
        <template v-if="detail?.deviceInfo?.problemDesc == '管网压力异常'">
          <p>系统类型：{{ detail?.deviceInfo?.deviceTypePname || '未知系统' }}</p>
          <p>设备类型：{{ detail?.deviceInfo?.deviceTypeName || '未知设备' }}</p>
        </template>
        <template v-else>
          <p v-if="detail.deviceClassification == '3' || detail.deviceClassification == '6'">
            IMEI：{{ detail?.deviceInfo?.deviceNum || '无' }}
          </p>
          <p v-else>设备编号：{{ detail?.deviceInfo?.deviceId || '无' }}</p>
        </template>
        <p v-if="detail.deviceClassification == '1' && switchCode(detail) != '--'">
          主机回路点位： {{ switchCode(detail) }}
        </p>
        <p v-if="detail.deviceClassification == '1'">二次码： {{ detail.twoCode || '未采集' }}</p>
        <p>系统类型：{{ detail?.deviceInfo?.deviceTypePname || '未知系统' }}</p>
        <p>设备类型：{{ detail?.deviceInfo?.deviceTypeName || '未知设备' }}</p>
        <p class="flex">
          <span class="shrink-0">设备位置：</span>
          <n-ellipsis line-clamp="1">{{ detail._deviceAddr }}</n-ellipsis>
          <n-button
            class="shrink-0 !ml-[10px]"
            type="primary"
            text
            size="small"
            @click="handleLocationClick"
            :disabled="!detail.deviceInfo?.deviceId"
          >
            <template #icon>
              <n-icon><IconLocation /></n-icon>
            </template>
            位置
          </n-button>
        </p>
        <p class="flex">
          <span class="shrink-0">品牌型号：</span>
          <n-ellipsis line-clamp="1">{{ detail._produceBrandAndModel }}</n-ellipsis>
        </p>
        <p>安装日期：{{ detail._installDate }}</p>
      </div>
    </ComBoxB>

    <ComBoxB title="催促记录" :is-empty="!urgeDataList.length">
      <div :class="$style.urgeRow" v-for="(item, idx) of urgeDataList" :key="idx">
        <p>
          <n-ellipsis line-clamp="1" style="max-width: 150px">{{ item.operatorName }}</n-ellipsis>
        </p>
        <p>{{ item.operatorTel }}</p>
        <p>
          <n-ellipsis line-clamp="1">{{ item.createTime }}</n-ellipsis>
        </p>
      </div>
    </ComBoxB>
    <DeviceLocation v-model:show="showDrawer" :device-list="deviceList" />
  </div>
</template>

<script setup lang="ts">
import ComBoxB from '@/components/box/ComBoxB.vue';
import { FlFilledLocationRipple as IconLocation } from '@kalimahapps/vue-icons';
import { getDisposeEventHazardInfo, getDisposeEventHazardUrgeRecord } from '@/views/inspect-com/fetchData.ts';
import { getFullFileUrl, getFullThumbnailUrl } from '@/utils/fileUrl.ts';
import { IObj } from '@/types';
import { ref } from 'vue';
import { renderCustomToolbar } from '@/common/hooks/useImagePreview.ts';
import { normalizeAddress, switchCode } from './util.ts';
import DeviceLocation from './DeviceLocation.vue';

const props = defineProps({
  data: {
    type: Object,
    default: () => {},
  },
});

const detail = ref<IObj<any>>({});
const urgeDataList = ref<{ operatorName: string; operatorTel: string; createTime: string }[]>([]);

// 设备信息
const deviceList = ref<any[]>([]);

const showDrawer = ref(false);
function handleLocationClick() {
  showDrawer.value = true;
}

function getData() {
  getDisposeEventHazardInfo({
    id: props.data.disposeId,
    eventType: props.data.disposeEventType,
  }).then((res) => {
    const data = res.data || {};
    const deviceInfo = data.deviceInfo;

    data._deviceAddr = normalizeAddress(data);

    data._deviceAddr = normalizeAddress(deviceInfo);
    const produceInfo = deviceInfo?.produceInfo || {};
    data._produceBrandAndModel = (produceInfo.brand || '') + (produceInfo.model || '') || '--';

    const installInfo = deviceInfo?.installInfo || {};
    data._installDate = installInfo?.install_date || '--';

    detail.value = data;
    deviceList.value.push({
      deviceId: deviceInfo.deviceId,
      deviceNum: deviceInfo.deviceNum,
      deviceTypeId: deviceInfo.deviceTypeId,
      deviceAddress: deviceInfo.deviceAddress,
      buildingId: deviceInfo.buildingId,
      floorId: deviceInfo.floorId,
      mapX: deviceInfo.mapX,
      mapY: deviceInfo.mapY,
      mapZ: deviceInfo.mapZ,
      latitude: deviceInfo.latitude,
      longitude: deviceInfo.longitude,
    });

    // 获取催促记录
    getDisposeEventHazardUrgeRecord({
      disposeId: data.disposeId,
      eventSourceId: data.hazardSource,
      eventType: data.eventType,
      hazardEventId: data.id, // ?
      subCenterCode: deviceInfo?.subCenterCode,
    }).then((res) => {
      res.data = res.data.concat(res.data);
      urgeDataList.value = res.data || [];
    });
  });
}

// init
getData();

defineOptions({ name: 'InspectInfoHazard' });
</script>

<style module lang="scss">
.InspectInfoHazard {
  > div {
    margin-bottom: 15px;
  }

  .imageGroup {
    display: grid;
    grid-template-columns: repeat(auto-fit, 80px);
    gap: 15px;
    margin-top: 10px;

    > div {
      background: rgba(0, 0, 0, 0.1);
    }
  }

  .urgeRow {
    display: grid;
    grid-template-columns: auto auto 1fr;
    column-gap: 5px;
    margin-top: 5px;

    > p {
      line-height: 1.2;
    }
  }
}
</style>
