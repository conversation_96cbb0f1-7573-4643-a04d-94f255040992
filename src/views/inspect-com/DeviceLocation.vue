c
<template>
  <ComDialogC title="查看点位" :width="1000" :height="600" :close-on-esc="false">
    <FloorGis :device-list="deviceList" />

    <VideoDia v-model:show="DeviceLocService.showVideo.value" :device-id="deviceId || ''" />
  </ComDialogC>
</template>

<script setup lang="ts">
import { ref, computed, useAttrs, watch } from 'vue';
import ComDialogC from '@/components/dialog/ComDialogC.vue';
import { useRoute } from 'vue-router';
import FloorGis from '@/gis-floor/deviceLocGis/deviceLocGis.vue';
import { EGisType } from '@/gis-floor/constant';
import { IDeviceRow } from '@/gis-floor/type';
import VideoDia from '@/views/inspect-com/InspectVideoDia.vue';
import { DeviceLocService } from '@/gis-floor/deviceLocService';

const route = useRoute();
const $attr = useAttrs();

const props = withDefaults(
  defineProps<{
    deviceList: IDeviceRow[];
  }>(),
  {
    deviceList: () => [],
  }
);

const deviceId = computed(() => DeviceLocService.videoDeviceId.value);

defineOptions({ name: 'DeviceLocation' });
</script>

<style module lang="scss">
.content {
  position: relative;
  backdrop-filter: blur(3px);
}
</style>
