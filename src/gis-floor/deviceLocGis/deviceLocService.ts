import { ref } from 'vue';
import { GisBase } from '@/gis/GisBase';
import { PopupService } from '../popup/gisPopupSerivce';
import { POI_POPUP_TYPE } from '../popup/tpl';
import { IDeviceRow } from '../type';

export class DeviceLocGisService {
  static curBuildId = ref('');
  static curFloorId = ref('');

  static showVideo = ref(false);
  static videoDeviceId = ref('');

  static gisIns: any;
  static isReady = ref(false);
  static floorLoadFail = ref(false);

  static hoverPopup: any;
  static devicePopup: any;

  static async init() {
    window.gisIns = this.gisIns = await GisBase.create('deviceLocGis');
    this.isReady.value = true;
  }

  /**
   * @param buildId
   * @param floorId
   * @param mapType
   * @returns
   */
  static renderFloorGis() {
    //渲染平面图
    return new Promise<{ mapType: number; gisIns: any }>((resolve, reject) => {
      if (!DeviceLocGisService.curFloorId.value) {
        DeviceLocGisService.floorLoadFail.value = true;
        return reject({ msg: '没有楼层id' });
      }

      const buildId = JSON.parse(JSON.stringify(DeviceLocGisService.curBuildId.value));
      const floorId = JSON.parse(JSON.stringify(DeviceLocGisService.curFloorId.value));

      DeviceLocGisService.gisIns?.showFloorData(
        1,
        undefined, //单位id
        buildId, //楼栋id
        floorId, //楼层id
        undefined, //图纸地址 （用于查询不到室内GIS数据时自动跳转，可缺省(异常则走内部跳转)）
        function (mapType: number, success: boolean, objArgs: any, indoor: any) {
          if (!success) {
            DeviceLocGisService.floorLoadFail.value = true;
            return reject({ msg: '加载失败' });
          }

          DeviceLocGisService.floorLoadFail.value = false;

          // 室内楼层模式在此处注册
          DeviceLocGisService.registerGisEvent();

          resolve({
            mapType,
            gisIns: indoor,
          });
        }
      );
    });
  }

  static registerGisEvent() {
    // popup注册, 实例化气泡服务
    DeviceLocGisService.hoverPopup = new PopupService(DeviceLocGisService.gisIns);
    DeviceLocGisService.devicePopup = new PopupService(DeviceLocGisService.gisIns);

    //设备点位图标 点击事件
    DeviceLocGisService.gisIns.onDeviceSelected = this.onDeviceClick;
    //未拾取到任何数据
    DeviceLocGisService.gisIns.onNullSelected = this.onUnllClick;
  }

  static onDeviceClick(data: IDeviceRow, e: any, obj: any, target: any) {
    const _ins = DeviceLocGisService.gisIns;
    const isMultiple = DeviceLocGisService.gisIns.getViewType() === IndoorMap.ViewType.IndoorAreaVector3DM;
    const _curLyr = isMultiple
      ? _ins.getCurrentLayer().GetFloorInfoIconLayers()
      : _ins.getCurrentLayer().getIconLayer();

    const device_geo = _ins.getGeoObjectByClientXY(_curLyr, e.getX(), e.getY());

    DeviceLocGisService.videoDeviceId.value = '';

    if (!device_geo) return;
    // 摄像头设备
    if (data.deviceTypeId === _ins.options.videoBufferQueryVideoTypeCode) {
      DeviceLocGisService.videoDeviceId.value = data.deviceId;
      DeviceLocGisService.showVideo.value = true;
      return;
    }

    DeviceLocGisService.devicePopup.show(
      {
        _ui_popupType: POI_POPUP_TYPE.DeviceDetail,
        ...data,
      },
      device_geo.object.getScenePosition()
    );
  }

  static onUnllClick() {
    if (DeviceLocGisService.hoverPopup) DeviceLocGisService.hoverPopup.close();
    if (DeviceLocGisService.devicePopup) DeviceLocGisService.devicePopup.close();
  }

  static showPoiList(poiList: any[]) {
    if (poiList.length < 1) return;

    const _ins = DeviceLocGisService.gisIns;

    const isMultiple = _ins.getViewType() === IndoorMap.ViewType.IndoorAreaVector3DM;
    const curLyr = isMultiple ? _ins.getCurrentLayer() : undefined;

    const xyz = _ins.getDeviceFieldNameXYZ(_ins.getViewType());
    _ins.showFloorDataDevice(poiList, xyz[0], xyz[1], xyz[2], undefined, false, undefined, curLyr);
  }

  static resetData() {
    this.isReady.value = false;
    this.floorLoadFail.value = false;
    this.curBuildId.value = '';
    this.curFloorId.value = '';
  }

  static clearGis() {
    if (this.gisIns) this.gisIns.clearAll();
    this.resetData();
  }

  static destoryGis() {
    this.clearGis();
    window.gisIns = this.gisIns = null;
  }
}
