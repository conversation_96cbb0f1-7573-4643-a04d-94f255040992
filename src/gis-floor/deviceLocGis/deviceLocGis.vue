<template>
  <n-spin :show="!GisService.isReady" class="w-full h-full" content-class="w-full h-full">
    <div class="map-wrapper">
      <div id="deviceLocGis" class="map"></div>
      <div v-show="GisService.floorLoadFail.value" class="no-gis">
        <img class="empty-img" src="../assets/empty.png" alt="" srcset="" />
        <span class="empty-text">暂无楼层图</span>
      </div>
    </div>
  </n-spin>
</template>

<script setup lang="ts">
import { onMounted, onBeforeUnmount } from 'vue';
import { DeviceLocGisService as GisService } from './deviceLocService';
import { IDeviceRow } from '../type';

defineOptions({ name: 'DeviceLocGisComp' });

interface IProps {
  deviceList?: Array<IDeviceRow>;
}
const props = withDefaults(defineProps<IProps>(), {
  deviceList: () => [],
});

// 点位
const initDeviceLoc = async () => {
  // 根据props.deviceList赋值
  const list = props.deviceList.map((item) => ({
    ...item,
    buildId: item.buildingId,
  }));
  GisService.curBuildId.value = list[0].buildId;
  GisService.curFloorId.value = list[0].floorId;

  // 加载楼层和点位
  await GisService.renderFloorGis();
  GisService.showPoiList(list);
};

onMounted(async () => {
  try {
    await GisService.init();

    initDeviceLoc();
  } catch (e) {
    console.error('GIS PAGE:', e);
  }
});

onBeforeUnmount(() => {
  GisService.destoryGis();
});
</script>

<style scoped lang="scss">
.map-wrapper {
  @apply relative w-full h-full overflow-hidden;
  background-color: var(--skin-bg0);
  border: 1px solid;
  border-color: var(--skin-bd2);

  .map {
    @apply absolute top-0 left-0 w-full h-full;
    z-index: 0;
  }
  .no-gis {
    @apply absolute top-0 left-0 w-full h-full flex flex-col items-center justify-center gap-[10px] bg-[#112232];
    z-index: 1;
    font-size: 16px;
    color: #8897b8;
  }

  .floor-control {
    @apply absolute top-[20px] right-[10px];
    z-index: 2;
  }
}
</style>
