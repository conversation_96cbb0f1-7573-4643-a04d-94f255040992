import { ref } from 'vue';
import { GisBase } from '@/gis/GisBase';
import { PopupService } from './popup/gisPopupSerivce';
import { POI_POPUP_TYPE, POI_POPUP_TPL } from './popup/tpl';
import { IDeviceRow, IErecordUnit } from './type';
import { getErecordUnitInfo } from './featch';
import { EGisType, TGisType } from './constant';
import { InspectEditService as InspectEdit } from './inspectEditService';
import { InspectDetailService as InspectDetail } from './inspectDetailService';
import { DeviceLocService as DeviceLoc } from './deviceLocService';
import { IObj } from '@/types';

export async function getUnitInfo(id: string) {
  const defaultModel = 5;
  // 不能放到reset去做，会导致编辑/回显路径模式下切换出问题
  FloorGisService.curUnitInfo.value = null;
  FloorGisService.inspectList.value = [];
  try {
    const res: any = await getErecordUnitInfo(id);
    if (res.code === 'success' && res.data) {
      const { unitId, serviceModelCode, floorMapType } = res.data;

      const _info = {
        treeId: id,
        unitId,
        serviceModelCode,
        floorMapType,
      };

      // 合肥院，强制设置多楼层模式
      if (unitId === 'AHHF_QHHFY_20180408') {
        _info.floorMapType = IndoorMap.ViewType.IndoorAreaVector3DM;
      }
      FloorGisService.curUnitInfo.value = _info;
      return serviceModelCode;
    } else {
      return defaultModel;
    }
  } catch (error: any) {
    console.log(error);
    return defaultModel;
  }
}

export class FloorGisService {
  // 控制地图模式
  static gisType: TGisType | undefined = undefined;

  // 电子档案单位数据
  static curUnitInfo = ref<IErecordUnit | null>(null);

  static curBuildId = ref('');
  static curFloorId = ref('');
  // 用于多楼层模式，是否需要加载楼栋点位
  static needLoadPOI = true;
  // 缓存当前撒点数据
  static poiList: IDeviceRow[] = [];
  // 巡检点位
  static inspectList = ref<IDeviceRow[]>([]);

  static gisIns: any;
  static isReady = ref(false);
  static floorLoadFail = ref(false);

  static hoverPopup: any;
  static devicePopup: any;

  static async init() {
    window.gisIns = this.gisIns = await GisBase.create('floorGis');
    this.isReady.value = true;
  }

  /**
   * @param buildId
   * @param floorId
   * @param mapType
   * @returns
   */
  static renderFloorGis() {
    //渲染平面图
    return new Promise<{ mapType: number; gisIns: any }>((resolve, reject) => {
      if (!FloorGisService.curFloorId.value) {
        FloorGisService.floorLoadFail.value = true;
        return reject({ msg: '没有楼层id' });
      }

      let mapType = FloorGisService.curUnitInfo.value?.floorMapType || 1;
      // 设备位置查看
      if (FloorGisService.gisType === EGisType.DEVICELOC) mapType = 1;

      const buildId = JSON.parse(JSON.stringify(FloorGisService.curBuildId.value));
      const floorId = JSON.parse(JSON.stringify(FloorGisService.curFloorId.value));

      FloorGisService.gisIns?.showFloorData(
        mapType,
        undefined, //单位id
        buildId, //楼栋id
        floorId, //楼层id
        undefined, //图纸地址 （用于查询不到室内GIS数据时自动跳转，可缺省(异常则走内部跳转)）
        function (mapType: number, success: boolean, objArgs: any, indoor: any) {
          if (!success) {
            FloorGisService.floorLoadFail.value = true;
            return reject({ msg: '加载失败' });
          }

          FloorGisService.floorLoadFail.value = false;

          // 室内楼层模式在此处注册
          FloorGisService.registerGisEvent();
          // 多楼层
          if (mapType === IndoorMap.ViewType.IndoorAreaVector3DM) {
            // 视角重新定位
            // const indoorDataState = indoor.getIndoorDataState();
            // objArgs.cancelZoomToExtent = indoorDataState.source.multiplex;

            objArgs.callback = function (indoor: any) {
              indoor.getCurrentLayer().Expand({ interval: 1000 });
            };

            indoor.getCurrentLayer().SetCurrentFloorNum(FloorGisService.curFloorId.value, 1000);
          }
          // 单楼层楼层
          if (mapType === IndoorMap.ViewType.IndoorAreaVector) {
          }

          resolve({
            mapType,
            gisIns: indoor,
          });
        }
      );
    });
  }

  static registerGisEvent() {
    // popup注册, 实例化气泡服务
    FloorGisService.hoverPopup = new PopupService(FloorGisService.gisIns);
    FloorGisService.devicePopup = new PopupService(FloorGisService.gisIns);

    // 事件注册
    FloorGisService.gisIns.onMouseMove = this.onHover;
    //设备点位图标 点击事件
    FloorGisService.gisIns.onDeviceSelected = this.onDeviceClick;
    //未拾取到任何数据
    FloorGisService.gisIns.onNullSelected = this.onUnllClick;

    FloorGisService.gisIns.onFloorSelected = function (data, e, obj, target) {
      if (data.floorId === FloorGisService.curFloorId.value) return;
      FloorGisService.curFloorId.value = data.floorId;
    };
  }

  static onHover(e: any) {
    const _ins = FloorGisService.gisIns;
    // 未初始化Layer
    if (!_ins.getCurrentLayer()) return;

    const isMultiple = FloorGisService.gisIns.getViewType() === IndoorMap.ViewType.IndoorAreaVector3DM;
    const _curLyr = isMultiple
      ? _ins.getCurrentLayer().GetFloorInfoIconLayers()
      : _ins.getCurrentLayer().getIconLayer();

    const device_geo = _ins.getGeoObjectByClientXY(_curLyr, e.getX(), e.getY());
    if (!device_geo) return;
    const deviceData = device_geo.object.gsData;
    // 摄像头设备
    if (deviceData.deviceTypeId === _ins.options.videoBufferQueryVideoTypeCode) {
      let _popup;
      if (FloorGisService.gisType === EGisType.INSPECTEDIT || FloorGisService.gisType === EGisType.INSPECTPLANDETAIL)
        _popup = POI_POPUP_TYPE.PoiTips;
      if (FloorGisService.gisType === EGisType.INSPECTDETAIL) _popup = POI_POPUP_TYPE.InspectTime;

      FloorGisService.hoverPopup.show(
        {
          _ui_popupType: _popup,
          ...deviceData,
        },
        device_geo.object.getScenePosition()
      );
    }
  }

  static onDeviceClick(data: IDeviceRow, e: any, obj: any, target: any) {
    if (FloorGisService.gisType === EGisType.INSPECTEDIT) {
      InspectEdit.onDeviceClick(data, e, obj, target);
    }

    if (FloorGisService.gisType === EGisType.INSPECTDETAIL) {
      InspectDetail.onDeviceClick(data, e, obj, target);
    }

    if (FloorGisService.gisType === EGisType.DEVICELOC) {
      DeviceLoc.onDeviceClick(data, e, obj, target);
    }
  }

  static onUnllClick() {
    if (FloorGisService.hoverPopup) FloorGisService.hoverPopup.close();
    if (FloorGisService.devicePopup) FloorGisService.devicePopup.close();
  }

  static showPoiList(poiList: any[]) {
    if (poiList.length < 1) return;

    const _ins = FloorGisService.gisIns;

    const isMultiple = _ins.getViewType() === IndoorMap.ViewType.IndoorAreaVector3DM;
    const curLyr = isMultiple ? _ins.getCurrentLayer() : undefined;

    const xyz = _ins.getDeviceFieldNameXYZ(_ins.getViewType());
    _ins.showFloorDataDevice(poiList, xyz[0], xyz[1], xyz[2], undefined, false, undefined, curLyr);
  }

  static showInspectList(list: any[]) {
    FloorGisService.gisIns.AppendInspectionArrayToTargetLayer(
      list,
      undefined,
      undefined,
      undefined,
      false,
      undefined,
      true,
      1
    );
    FloorGisService.gisIns.render();
  }

  static async changeFloor() {
    this.onUnllClick();

    await FloorGisService.renderFloorGis();
  }

  static CollapseChange(isExpand: boolean) {
    const _ins = FloorGisService.gisIns;
    if (!_ins) return;

    if (isExpand) {
      _ins.getCurrentLayer().Expand({ interval: 1000 });
    } else {
      _ins.getCurrentLayer().Collapse({ interval: 1000 });
    }
  }

  static resetData() {
    this.isReady.value = false;
    this.floorLoadFail.value = false;
    this.curBuildId.value = '';
    this.curFloorId.value = '';
    this.poiList = [];
  }

  static clearGis() {
    if (this.gisIns) this.gisIns.clearAll();
    this.resetData();
  }

  static destoryGis() {
    this.clearGis();
    window.gisIns = this.gisIns = null;
  }
}
